<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 对话助手</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.6.0/styles/default.min.css">
    <!-- 添加markdown-it库 -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <!-- 添加 KaTeX 库 -->
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.js"></script>
    <!-- 添加 highlight.js 库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.6.0/highlight.min.js"></script>
    <!-- 添加封装的markdown渲染模块 -->
    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/contrib/auto-render.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.5.1/highlight.min.js"></script>
    <!-- 添加Mermaid库 -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@11.6.0/dist/mermaid.min.js"></script>
    <!-- 已在头部引入Mermaid，这里删除重复引用 -->
<style>
    /* 主要布局和通用样式 - 统一风格设计 */
    body, html {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
        margin: 0;
        padding: 0;
        height: 100%;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        background-attachment: fixed;
        color: #2d3748;
        line-height: 1.6;
        overflow-x: hidden;
    }

    .chat-container {
        position: relative !important;
        display: flex !important;
        flex-direction: column !important;
        height: 100vh !important;
        max-width: 1400px !important;
        margin: 0 auto !important;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1) !important;
        background: rgba(255, 255, 255, 0.95) !important;
        border-radius: 24px !important;
        backdrop-filter: blur(20px) !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        animation: containerFloat 6s ease-in-out infinite !important;
        overflow: hidden !important;
    }

    /* 容器浮动动画 */
    @keyframes containerFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(0.5deg); }
    }
    
    /* AI 对话助手标题优化 - 统一风格设计 */
    #aiChatSection h2 {
        color: #4a5568 !important;
        font-size: 2rem !important;
        font-weight: 700 !important;
        text-align: center !important;
        margin: 20px 0 !important;
        padding: 20px 0 !important;
        position: relative !important;
        letter-spacing: 0.5px !important;
    }

    #aiChatSection h2:after {
        content: "" !important;
        position: absolute !important;
        bottom: 0 !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: 80px !important;
        height: 3px !important;
        background: linear-gradient(90deg, #4299e1, #3182ce) !important;
        border-radius: 2px !important;
    }
    
    /* 聊天窗口优化 - 现代化设计 */
    #chatHistory {
        margin: 20px !important;
        padding: 25px !important;
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
        border-radius: 16px !important;
        border: 1px solid rgba(99, 102, 241, 0.1) !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
        max-height: 60vh !important;
        overflow-y: auto !important;
        scrollbar-width: thin !important;
        scrollbar-color: rgba(99, 102, 241, 0.3) transparent !important;
        position: relative !important;
    }

    /* 自定义聊天历史滚动条 - 统一风格设计 */
    #chatHistory::-webkit-scrollbar {
        width: 8px !important;
    }

    #chatHistory::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1) !important;
        border-radius: 10px !important;
        margin: 5px !important;
    }

    #chatHistory::-webkit-scrollbar-thumb {
        background: linear-gradient(180deg, rgba(66, 153, 225, 0.6), rgba(49, 130, 206, 0.6)) !important;
        border-radius: 10px !important;
        border: 2px solid transparent !important;
        background-clip: padding-box !important;
        transition: all 0.3s ease !important;
        animation: scrollbarPulse 2s ease-in-out infinite !important;
    }

    #chatHistory::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(180deg, rgba(66, 153, 225, 0.8), rgba(49, 130, 206, 0.8)) !important;
        transform: scaleX(1.2) !important;
        animation: none !important;
    }

    /* 滚动条脉冲动画 */
    @keyframes scrollbarPulse {
        0%, 100% { opacity: 0.6; }
        50% { opacity: 1; }
    }
    
    /* 按钮样式优化 - 统一风格设计 */
    #selectAllButton, .submit-button, .toggle-button {
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
        color: white !important;
        font-weight: 600 !important;
        padding: 12px 24px !important;
        border: none !important;
        border-radius: 12px !important;
        cursor: pointer !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        text-align: center !important;
        font-size: 0.95rem !important;
        letter-spacing: 0.5px !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        min-width: 140px !important;
        position: relative !important;
        overflow: hidden !important;
    }

    /* 按钮波纹效果 */
    #selectAllButton::before, .submit-button::before, .toggle-button::before {
        content: '' !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        width: 0 !important;
        height: 0 !important;
        border-radius: 50% !important;
        background: rgba(255, 255, 255, 0.3) !important;
        transform: translate(-50%, -50%) !important;
        transition: width 0.6s, height 0.6s !important;
    }

    #selectAllButton:active::before, .submit-button:active::before, .toggle-button:active::before {
        width: 300px !important;
        height: 300px !important;
    }

    #selectAllButton:hover, .submit-button:hover, .toggle-button:hover {
        background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%) !important;
        transform: translateY(-3px) scale(1.02) !important;
        box-shadow: 0 8px 25px rgba(66, 153, 225, 0.4), 0 4px 10px rgba(0, 0, 0, 0.15) !important;
    }

    #selectAllButton:active, .submit-button:active, .toggle-button:active {
        transform: translateY(-1px) scale(0.98) !important;
        box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3) !important;
    }
    
    /* 优化全选/取消全选按钮 */
    #selectAllButton {
        position: relative !important;
        overflow: hidden !important;
        background-color: #e6b84d !important;
        min-width: 140px !important; /* 增加宽度以容纳文本 */
    }
    
    #selectAllButton:before {
        content: "✓" !important;
        margin-right: 6px !important;
        font-weight: bold !important;
    }
    
    /* 保存设置按钮优化 */
    .submit-button {
        background-color: #e6b84d !important; /* 主题黄色 */
        position: relative !important;
        overflow: hidden !important;
        margin-left: auto !important;
        min-width: 120px !important;
    }
    
    .submit-button:after {
        content: "" !important;
        position: absolute !important;
        width: 100% !important;
        height: 100% !important;
        top: 0 !important;
        left: -100% !important;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent) !important;
        transition: 0.5s ease !important;
    }
    
    .submit-button:hover:after {
        left: 100% !important;
    }
    
    /* 优化选项卡标题区域的布局 */
    div[style*="display: flex; justify-content: space-between;"] {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin: 15px 10px 10px !important;
        padding: 0 5px !important;
    }
    
    /* 增强文件选择容器样式 - 统一风格 */
    #file-selection-container {
        border: 1px solid rgba(66, 153, 225, 0.2) !important;
        border-radius: 16px !important;
        padding: 20px !important;
        background: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%) !important;
        margin: 16px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04) !important;
        max-height: 300px !important;
        overflow-y: auto !important;
        backdrop-filter: blur(10px) !important;
    }
    
    /* 增强用户和AI聊天样式 - 现代化气泡设计 */
    .system-message, .you-message, .ai-message {
        margin-bottom: 24px !important;
        padding: 18px 24px !important;
        border-radius: 20px !important;
        max-width: 75% !important;
        position: relative !important;
        overflow-wrap: break-word !important;
        word-wrap: break-word !important;
        hyphens: auto !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.05) !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        line-height: 1.7 !important;
        backdrop-filter: blur(10px) !important;
        animation: messageSlideIn 0.5s ease-out !important;
    }

    /* 消息滑入动画 */
    @keyframes messageSlideIn {
        from {
            opacity: 0;
            transform: translateY(20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    /* 增强悬停效果 */
    .system-message:hover, .you-message:hover, .ai-message:hover {
        transform: translateY(-4px) scale(1.01) !important;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08) !important;
    }

    .you-message {
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
        color: white !important;
        margin-left: auto !important;
        border-bottom-right-radius: 8px !important;
    }

    .ai-message {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
        color: white !important;
        margin-right: auto !important;
        border-bottom-left-radius: 8px !important;
    }

    .system-message {
        background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%) !important;
        color: white !important;
        margin: 0 auto !important;
        text-align: center !important;
        border-radius: 20px !important;
    }
    
    /* 保存设置按钮 - 统一风格卡片设计 */
    .options-container {
        margin-top: 24px !important;
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 16px !important;
        border: 1px solid rgba(66, 153, 225, 0.2) !important;
        border-radius: 16px !important;
        padding: 24px !important;
        max-width: none !important;
        background: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%) !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04) !important;
        backdrop-filter: blur(10px) !important;
    }

    /* 选项卡内的表单项 - 统一风格卡片 */
    .options-container > div {
        flex: 0 0 200px !important;
        display: flex !important;
        align-items: center !important;
        border: 1px solid rgba(66, 153, 225, 0.15) !important;
        border-radius: 12px !important;
        padding: 12px 16px !important;
        min-width: 200px !important;
        max-width: 200px !important;
        background: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%) !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
    }

    .options-container > div:hover {
        border-color: rgba(66, 153, 225, 0.3) !important;
        box-shadow: 0 4px 20px rgba(66, 153, 225, 0.15), 0 2px 8px rgba(0, 0, 0, 0.08) !important;
        transform: translateY(-3px) scale(1.02) !important;
    }
    
    /* 标签样式优化 */
    .options-container label {
        color: #4a5568 !important;
        font-weight: 500 !important;
        margin-right: 8px !important;
        white-space: nowrap !important;
        font-size: 0.95rem !important;
    }

    /* 选择框样式 */
    .options-container select, .options-container input[type="number"] {
        border: 1px solid rgba(66, 153, 225, 0.3) !important;
        border-radius: 8px !important;
        padding: 8px 12px !important;
        background-color: white !important;
        color: #2d3748 !important;
        font-size: 0.95rem !important;
        transition: all 0.3s ease !important;
        flex: 1 !important;
    }

    .options-container select:focus, .options-container input[type="number"]:focus {
        border-color: #4299e1 !important;
        outline: none !important;
        box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2) !important;
    }
    
    /* 聊天输入区域优化 - 统一风格浮动设计 */
    .chat-input {
        position: relative !important;
        display: flex !important;
        justify-content: center !important;
        width: 92% !important;
        max-width: 1200px !important;
        margin: 24px auto !important;
        padding: 20px !important;
        background: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%) !important;
        border: 1px solid rgba(66, 153, 225, 0.1) !important;
        border-radius: 20px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04) !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        z-index: 50 !important;
        backdrop-filter: blur(15px) !important;
    }

    /* 输入区域聚焦时的样式增强 */
    .chat-input-focused {
        box-shadow: 0 12px 40px rgba(66, 153, 225, 0.15), 0 4px 12px rgba(0, 0, 0, 0.08) !important;
        border-color: rgba(66, 153, 225, 0.3) !important;
        transform: translateY(-2px) !important;
    }
    
    .chat-input-wrapper {
        display: flex !important;
        width: 100% !important; /* 修改为100%，使其充满chat-input容器 */
        align-items: flex-end !important;
        position: relative !important;
        transition: all 0.3s ease !important;
    }
    
    #userInput {
        flex: 1 !important;
        min-height: 56px !important;
        max-height: 200px !important;
        padding: 16px 24px !important;
        border: 2px solid rgba(66, 153, 225, 0.1) !important;
        border-radius: 16px !important;
        font-size: 1rem !important;
        resize: none !important;
        overflow-y: auto !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        background: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%) !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
        line-height: 1.6 !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
        color: #2d3748 !important;
    }

    #userInput:focus {
        min-height: 80px !important;
        outline: none !important;
        border-color: rgba(66, 153, 225, 0.4) !important;
        box-shadow: 0 0 0 4px rgba(66, 153, 225, 0.1), 0 4px 20px rgba(66, 153, 225, 0.15) !important;
        transform: translateY(-2px) scale(1.01) !important;
        background: #ffffff !important;
    }
    
    .button-group {
        display: flex !important;
        flex-direction: column !important;
        gap: 10px !important;
        margin-left: 15px !important;
        transition: transform 0.3s ease !important;
    }
    
    /* 发送按钮强调样式 */
    #sendButton {
        position: relative !important;
        overflow: hidden !important;
    }
    
    #sendButton:after {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background: rgba(255, 255, 255, 0.2) !important;
        transform: translateX(-100%) !important;
        transition: transform 0.5s ease !important;
    }
    
    #sendButton:hover:after {
        transform: translateX(0) !important;
    }
    
    /* 输入框提示样式 */
    .input-tip {
        position: absolute !important;
        right: 10px !important;
        bottom: 10px !important;
        font-size: 0.8rem !important;
        color: #aaa !important;
        pointer-events: none !important;
        opacity: 0.7 !important;
        padding: 3px 8px !important;
        background-color: rgba(255, 255, 255, 0.8) !important;
        border-radius: 4px !important;
        transition: opacity 0.3s ease !important;
    }
    
    /* 自定义滚动条 */
    #userInput::-webkit-scrollbar {
        width: 8px !important;
    }
    
    #userInput::-webkit-scrollbar-track {
        background: rgba(240, 230, 204, 0.2) !important;
        border-radius: 4px !important;
    }
    
    #userInput::-webkit-scrollbar-thumb {
        background-color: rgba(230, 184, 77, 0.4) !important;
        border-radius: 4px !important;
        border: 2px solid transparent !important;
        background-clip: content-box !important;
    }
    
    #userInput::-webkit-scrollbar-thumb:hover {
        background-color: rgba(230, 184, 77, 0.6) !important;
    }
    
    /* 确保聊天历史底部有足够空间，避免被固定输入框遮挡 */
    .chat-history {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        background-color: #fffcf2; /* 更淡的背景色 */
        border-radius: 8px;
        margin: 10px;
        box-shadow: inset 0 0 5px rgba(0,0,0,0.05);
        padding-bottom: 20px !important; /* 减少底部内边距，之前是为了给固定输入框留空间 */
        margin-bottom: 10px !important;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .chat-input-wrapper {
            width: 95% !important;
            flex-direction: column !important;
            align-items: stretch !important;
        }
        
        .button-group {
            flex-direction: row !important;
            margin-left: 0 !important;
            margin-top: 10px !important;
            justify-content: space-between !important;
        }
        
        #userInput {
            border-radius: 8px !important;
        }
        
        #sendButton, #newChatButton, #downloadButton {
            flex: 1 !important;
            margin-left: 5px !important;
            margin-right: 5px !important;
            font-size: 0.9rem !important;
        }
        
        .chat-history {
            padding-bottom: 150px !important; /* 为竖排按钮留出更多空间 */
        }
    }
    
    #sendButton, #newChatButton, #downloadButton {
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
        color: white !important;
        border: none !important;
        border-radius: 12px !important;
        padding: 0 24px !important;
        margin-left: 12px !important;
        cursor: pointer !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        font-weight: 600 !important;
        height: 52px !important;
        box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    #sendButton:hover, #newChatButton:hover, #downloadButton:hover {
        background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%) !important;
        transform: translateY(-3px) scale(1.05) !important;
        box-shadow: 0 8px 25px rgba(66, 153, 225, 0.4), 0 4px 10px rgba(0, 0, 0, 0.15) !important;
    }

    #sendButton:active, #newChatButton:active, #downloadButton:active {
        transform: translateY(-1px) scale(0.98) !important;
        box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3) !important;
    }

    #sendButton:disabled, #newChatButton:disabled, #downloadButton:disabled {
        background: linear-gradient(135deg, #a0aec0 0%, #718096 100%) !important;
        cursor: not-allowed !important;
        transform: none !important;
        box-shadow: none !important;
    }
    
    /* 文件选择区域 - 统一风格卡片设计 */
    .file-selection {
        padding: 20px !important;
        background: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%) !important;
        border: 1px solid rgba(66, 153, 225, 0.1) !important;
        border-radius: 16px !important;
        margin: 16px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04) !important;
        backdrop-filter: blur(10px) !important;
    }

    #file-selection-container {
        display: grid !important;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)) !important;
        gap: 16px !important;
        margin-top: 16px !important;
    }

    .file-item {
        background: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%) !important;
        border: 1px solid rgba(66, 153, 225, 0.15) !important;
        border-radius: 16px !important;
        padding: 18px 24px !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 4px 20px rgba(66, 153, 225, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04) !important;
        cursor: pointer !important;
        position: relative !important;
        overflow: visible !important;
        backdrop-filter: blur(10px) !important;
        margin-bottom: 12px !important;
        min-height: 60px !important;
    }

    /* 文件项悬浮动态效果 */
    .file-item:hover {
        background: linear-gradient(145deg, #f8fafc 0%, #e2e8f0 100%) !important;
        transform: translateY(-8px) scale(1.03) !important;
        box-shadow: 0 20px 40px rgba(66, 153, 225, 0.2), 0 8px 16px rgba(0, 0, 0, 0.1) !important;
        border-color: rgba(66, 153, 225, 0.3) !important;
    }

    /* 文件项选中状态 */
    .file-item.selected {
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
        border-color: rgba(66, 153, 225, 0.4) !important;
        color: white !important;
        box-shadow: 0 12px 30px rgba(66, 153, 225, 0.4), 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        transform: translateY(-4px) scale(1.02) !important;
    }

    /* 文件项选中状态悬浮效果 */
    .file-item.selected:hover {
        transform: translateY(-10px) scale(1.04) !important;
        box-shadow: 0 25px 50px rgba(66, 153, 225, 0.5), 0 10px 20px rgba(0, 0, 0, 0.15) !important;
    }

    /* 文件项内容样式 - 优化布局 */
    .file-item .file-info {
        display: flex !important;
        align-items: center !important;
        flex: 1 !important;
        cursor: pointer !important;
        min-width: 0 !important; /* 允许内容收缩 */
        margin-right: 10px !important; /* 减少右边距，只留少量间隙 */
    }

    .file-item .file-name {
        margin-left: 12px !important;
        font-weight: 500 !important;
        color: #2d3748 !important;
        transition: color 0.3s ease !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        flex: 1 !important;
    }

    .file-item.selected .file-name {
        color: white !important;
        font-weight: 600 !important;
    }

    /* 复选框样式优化 */
    .file-item input[type="checkbox"] {
        width: 18px !important;
        height: 18px !important;
        border: 2px solid rgba(66, 153, 225, 0.3) !important;
        border-radius: 4px !important;
        transition: all 0.3s ease !important;
        flex-shrink: 0 !important; /* 防止收缩 */
        z-index: 5 !important;
        position: relative !important;
    }

    .file-item input[type="checkbox"]:checked {
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
        border-color: #4299e1 !important;
    }
    
    .file-info {
        display: flex;
        align-items: center;
        cursor: pointer;
        flex: 1;
    }
    
    .file-name {
        margin-left: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
    }
    
    .delete-button {
        background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%) !important;
        color: white !important;
        border: none !important;
        border-radius: 8px !important;
        padding: 6px 12px !important;
        cursor: pointer !important;
        font-size: 0.8rem !important;
        font-weight: 500 !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 2px 8px rgba(245, 101, 101, 0.3), 0 1px 3px rgba(0, 0, 0, 0.1) !important;
        flex-shrink: 0 !important;
        margin-left: 8px !important;
        min-width: 50px !important;
        white-space: nowrap !important;
    }

    .delete-button:hover {
        background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%) !important;
        transform: translateY(-2px) scale(1.05) !important;
        box-shadow: 0 4px 15px rgba(245, 101, 101, 0.4), 0 2px 6px rgba(0, 0, 0, 0.15) !important;
    }

    .delete-button:active {
        transform: translateY(0) scale(1.02) !important;
        box-shadow: 0 1px 4px rgba(245, 101, 101, 0.3) !important;
    }

    /* 文件项加载动画 */
    @keyframes fileItemSlideIn {
        from {
            opacity: 0;
            transform: translateY(20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .file-item {
        animation: fileItemSlideIn 0.5s ease-out !important;
    }

    /* 文件项波纹效果 */
    .file-item::before {
        content: '' !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        width: 0 !important;
        height: 0 !important;
        border-radius: 50% !important;
        background: rgba(66, 153, 225, 0.1) !important;
        transform: translate(-50%, -50%) !important;
        transition: width 0.6s, height 0.6s !important;
        pointer-events: none !important;
        z-index: 0 !important;
    }

    .file-item:active::before {
        width: 300px !important;
        height: 300px !important;
    }

    /* 确保内容在波纹效果之上 */
    .file-item .file-info,
    .file-item .delete-button {
        position: relative !important;
        z-index: 1 !important;
    }

    /* 文件项悬浮时的光晕效果 */
    .file-item::after {
        content: '' !important;
        position: absolute !important;
        top: -2px !important;
        left: -2px !important;
        right: -2px !important;
        bottom: -2px !important;
        background: linear-gradient(45deg, rgba(66, 153, 225, 0.1), rgba(49, 130, 206, 0.1)) !important;
        border-radius: 18px !important;
        opacity: 0 !important;
        transition: opacity 0.3s ease !important;
        z-index: -1 !important;
        pointer-events: none !important;
    }

    .file-item:hover::after {
        opacity: 1 !important;
    }

    /* 响应式优化 - 文件选项框宽度调整 */
    @media (max-width: 768px) {
        #file-selection-container {
            grid-template-columns: repeat(auto-fill, minmax(240px, 1fr)) !important;
        }

        .delete-button {
            padding: 5px 10px !important;
            font-size: 0.75rem !important;
            min-width: 45px !important;
        }
    }

    @media (max-width: 480px) {
        #file-selection-container {
            grid-template-columns: 1fr !important;
        }

        .delete-button {
            padding: 4px 8px !important;
            font-size: 0.7rem !important;
            min-width: 40px !important;
        }

        .file-item {
            padding: 12px 16px !important;
            min-height: 50px !important;
        }
    }
    
    .file-selection-controls {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
    
    /* 这些样式已经在上面统一定义了，这里删除重复定义 */
    
    /* 自定义表单控件样式 - 现代化设计 */
    .settings-panel {
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
        padding: 24px !important;
        border: 1px solid rgba(99, 102, 241, 0.1) !important;
        border-radius: 16px !important;
        margin: 16px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04) !important;
        backdrop-filter: blur(10px) !important;
    }

    .settings-row {
        display: flex !important;
        flex-wrap: wrap !important;
        margin-bottom: 16px !important;
        align-items: center !important;
        gap: 12px !important;
    }

    .settings-item {
        margin-right: 0 !important;
        display: flex !important;
        align-items: center !important;
        margin-bottom: 12px !important;
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
        padding: 12px 16px !important;
        border-radius: 12px !important;
        border: 1px solid rgba(99, 102, 241, 0.1) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    .settings-item:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 20px rgba(99, 102, 241, 0.15), 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    }

    .settings-item label {
        margin-right: 12px !important;
        color: #2c3e50 !important;
        font-weight: 600 !important;
        font-size: 0.95rem !important;
    }

    .settings-item select, .settings-item input[type="number"] {
        padding: 8px 12px !important;
        border: 1px solid rgba(99, 102, 241, 0.2) !important;
        border-radius: 8px !important;
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
        color: #2c3e50 !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        font-size: 0.95rem !important;
    }

    .settings-item select:focus, .settings-item input[type="number"]:focus {
        outline: none !important;
        border-color: rgba(99, 102, 241, 0.4) !important;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), 0 2px 8px rgba(99, 102, 241, 0.15) !important;
        background: #ffffff !important;
    }
    
    .settings-submit {
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
        color: white !important;
        border: none !important;
        border-radius: 12px !important;
        padding: 12px 24px !important;
        cursor: pointer !important;
        margin-top: 16px !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        font-weight: 600 !important;
        font-size: 0.95rem !important;
        box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }

    .settings-submit:hover {
        background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%) !important;
        transform: translateY(-3px) scale(1.05) !important;
        box-shadow: 0 8px 25px rgba(66, 153, 225, 0.4), 0 4px 10px rgba(0, 0, 0, 0.15) !important;
    }

    .settings-submit:active {
        transform: translateY(-1px) scale(0.98) !important;
        box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3) !important;
    }
    
    /* 代码块样式 - 现代化设计 */
    pre {
        border-radius: 12px !important;
        padding: 20px !important;
        background: linear-gradient(145deg, #1a202c 0%, #2d3748 100%) !important;
        border: 1px solid rgba(99, 102, 241, 0.2) !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        margin: 20px 0 !important;
        position: relative !important;
        overflow: auto !important;
        backdrop-filter: blur(10px) !important;
    }

    pre code {
        font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace !important;
        font-size: 0.9em !important;
        line-height: 1.6 !important;
        color: #e2e8f0 !important;
    }
    
    /* 代码块标题和复制按钮组 */
    .code-header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 8px 16px !important;
        background-color: #f0f0f0 !important;
        border-top-left-radius: 8px !important;
        border-top-right-radius: 8px !important;
        border: 1px solid #ebebeb !important;
        border-bottom: none !important;
        margin-top: 16px !important;
        font-family: 'Fira Code', 'Consolas', monospace !important;
        font-size: 0.85em !important;
        color: #555 !important;
    }
    
    /* 针对代码块的配套样式 */
    .code-block-wrapper {
        position: relative !important;
        margin: 16px 0 !important;
    }
    
    .code-block-wrapper pre {
        margin-top: 0 !important;
        border-top-left-radius: 0 !important;
        border-top-right-radius: 0 !important;
    }
    
    /* 复制按钮样式 - 统一风格设计 */
    .copy-button {
        background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(49, 130, 206, 0.1) 100%) !important;
        color: #e2e8f0 !important;
        border: 1px solid rgba(66, 153, 225, 0.3) !important;
        border-radius: 8px !important;
        padding: 6px 12px !important;
        font-size: 0.85em !important;
        cursor: pointer !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        backdrop-filter: blur(10px) !important;
        font-weight: 500 !important;
    }

    .copy-button:hover {
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
        color: white !important;
        border-color: rgba(66, 153, 225, 0.5) !important;
        transform: translateY(-2px) scale(1.05) !important;
        box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3) !important;
    }
    
    /* 行号样式 */
    .hljs-line-numbers {
        counter-reset: line !important;
        padding-left: 3.5em !important;
        position: relative !important;
    }
    
    .hljs-line-numbers code::before {
        counter-increment: line !important;
        content: counter(line) !important;
        position: absolute !important;
        left: 0 !important;
        width: 2.5em !important;
        text-align: right !important;
        color: #999 !important;
        padding-right: 0.5em !important;
        border-right: 1px solid #ddd !important;
        user-select: none !important;
    }
    
    /* 表格样式美化 - 统一风格设计 */
    table {
        border-collapse: collapse !important;
        width: 100% !important;
        margin: 20px 0 !important;
        border-radius: 12px !important;
        overflow: hidden !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04) !important;
        background: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%) !important;
    }

    th {
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
        color: white !important;
        font-weight: 600 !important;
        text-align: left !important;
        padding: 16px 20px !important;
        border-bottom: none !important;
        font-size: 0.95rem !important;
        letter-spacing: 0.5px !important;
    }

    td {
        padding: 14px 20px !important;
        border-bottom: 1px solid rgba(66, 153, 225, 0.1) !important;
        color: #2d3748 !important;
        font-size: 0.95rem !important;
    }

    tr:nth-child(even) {
        background: linear-gradient(145deg, #f7fafc 0%, #edf2f7 100%) !important;
    }

    tr:hover {
        background: linear-gradient(135deg, rgba(66, 153, 225, 0.05) 0%, rgba(49, 130, 206, 0.05) 100%) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
    }
    
    /* 链接样式 - 统一风格设计 */
    a {
        color: #4299e1 !important;
        text-decoration: none !important;
        border-bottom: 2px solid transparent !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        position: relative !important;
        font-weight: 500 !important;
    }

    a:hover {
        color: #3182ce !important;
        border-bottom: 2px solid #4299e1 !important;
        transform: translateY(-1px) !important;
    }

    /* 引用块美化 - 统一风格设计 */
    blockquote {
        border-left: 4px solid #4299e1 !important;
        padding: 16px 24px !important;
        margin: 20px 0 !important;
        background: linear-gradient(145deg, rgba(66, 153, 225, 0.05) 0%, rgba(49, 130, 206, 0.05) 100%) !important;
        border-radius: 0 12px 12px 0 !important;
        font-style: italic !important;
        color: #2d3748 !important;
        box-shadow: 0 4px 15px rgba(66, 153, 225, 0.1) !important;
        backdrop-filter: blur(10px) !important;
    }

    blockquote p {
        margin: 0 !important;
        font-size: 1.05rem !important;
        line-height: 1.7 !important;
    }
    
    /* 数学公式渲染优化 - 统一风格设计 */
    .katex-display {
        padding: 16px 20px !important;
        margin: 20px 0 !important;
        overflow-x: auto !important;
        background: linear-gradient(145deg, rgba(66, 153, 225, 0.05) 0%, rgba(49, 130, 206, 0.05) 100%) !important;
        border-radius: 12px !important;
        border: 1px solid rgba(66, 153, 225, 0.2) !important;
        box-shadow: 0 8px 32px rgba(66, 153, 225, 0.1), 0 2px 8px rgba(0, 0, 0, 0.04) !important;
        backdrop-filter: blur(10px) !important;
    }
    
    /* 响应式设计 - 现代化优化 */
    @media (max-width: 1200px) {
        .chat-container {
            max-width: 95% !important;
            margin: 10px auto !important;
            border-radius: 16px !important;
        }

        #chatHistory {
            margin: 15px !important;
            padding: 20px !important;
        }
    }

    @media (max-width: 768px) {
        body, html {
            font-size: 14px !important;
        }

        .chat-container {
            border-radius: 12px !important;
            margin: 5px auto !important;
            animation: none !important; /* 移动端禁用浮动动画 */
        }

        #aiChatSection h2 {
            font-size: 1.5rem !important;
            margin: 15px 0 !important;
        }

        .system-message, .you-message, .ai-message {
            max-width: 90% !important;
            padding: 14px 18px !important;
            margin-bottom: 16px !important;
        }

        .settings-item {
            margin-right: 8px !important;
            width: calc(50% - 16px) !important;
            min-width: auto !important;
        }

        #file-selection-container {
            grid-template-columns: 1fr !important;
            gap: 12px !important;
        }

        .chat-input {
            width: 95% !important;
            padding: 15px !important;
            margin: 15px auto !important;
        }

        #userInput {
            padding: 12px 16px !important;
            min-height: 48px !important;
        }

        #sendButton, #newChatButton, #downloadButton {
            height: 48px !important;
            padding: 0 16px !important;
            margin-left: 8px !important;
            font-size: 0.9rem !important;
        }
    }
    
    @media (max-width: 480px) {
        body, html {
            font-size: 13px !important;
        }

        .chat-container {
            height: 100vh !important;
            border-radius: 0 !important;
            margin: 0 !important;
        }

        #aiChatSection h2 {
            font-size: 1.3rem !important;
            padding: 15px 0 !important;
        }

        #chatHistory {
            margin: 10px !important;
            padding: 15px !important;
            border-radius: 12px !important;
        }

        .system-message, .you-message, .ai-message {
            max-width: 95% !important;
            padding: 12px 16px !important;
            margin-bottom: 12px !important;
            border-radius: 16px !important;
        }

        .chat-input {
            width: 98% !important;
            padding: 12px !important;
            margin: 10px auto !important;
            border-radius: 16px !important;
        }

        #userInput {
            padding: 10px 14px !important;
            min-height: 44px !important;
            border-radius: 12px !important;
        }

        #sendButton, #newChatButton, #downloadButton {
            height: 44px !important;
            padding: 0 12px !important;
            margin-left: 6px !important;
            font-size: 0.85rem !important;
            border-radius: 10px !important;
        }

        .settings-item {
            width: 100% !important;
            margin-right: 0 !important;
            margin-bottom: 8px !important;
        }

        #file-selection-container {
            grid-template-columns: 1fr !important;
            gap: 8px !important;
            padding: 12px !important;
        }

        .file-item {
            padding: 12px 16px !important;
            border-radius: 10px !important;
        }

        .options-container {
            padding: 16px !important;
            gap: 8px !important;
        }

        .options-container > div {
            min-width: 100% !important;
            max-width: 100% !important;
            margin-bottom: 8px !important;
        }
    }

    /* Markdown rendering styles */
    .rendered-markdown h1, .rendered-markdown h2, .rendered-markdown h3, 
    .rendered-markdown h4, .rendered-markdown h5, .rendered-markdown h6 {
        margin-top: 1.5em;
        margin-bottom: 0.5em;
        font-weight: 600;
        line-height: 1.25;
    }
    
    .rendered-markdown h1 {
        font-size: 1.8em;
        border-bottom: 1px solid #eaecef;
        padding-bottom: 0.3em;
    }
    
    .rendered-markdown h2 {
        font-size: 1.5em;
        border-bottom: 1px solid #eaecef;
        padding-bottom: 0.3em;
    }
    
    .rendered-markdown h3 {
        font-size: 1.3em;
    }
    
    .rendered-markdown h4 {
        font-size: 1.1em;
    }
    
    .rendered-markdown p, .rendered-markdown ul, .rendered-markdown ol, 
    .rendered-markdown blockquote, .rendered-markdown pre {
        margin-top: 0;
        margin-bottom: 1em;
    }
    
    .rendered-markdown ul, .rendered-markdown ol {
        padding-left: 2em;
    }
    
    .rendered-markdown blockquote {
        border-left: 3px solid #dfe2e5;
        color: #6a737d;
        padding: 0 1em;
        margin-left: 0;
    }
    
    .rendered-markdown pre {
        padding: 16px;
        overflow: auto;
        font-size: 0.9em;
        line-height: 1.45;
        background-color: #f6f8fa;
        border-radius: 3px;
        position: relative;
    }
    
    .rendered-markdown code {
        background-color: rgba(27, 31, 35, 0.05);
        border-radius: 3px;
        font-family: "SF Mono", "Consolas", "Liberation Mono", "Menlo", monospace;
        font-size: 0.9em;
        padding: 0.2em 0.4em;
    }
    
    .rendered-markdown pre code {
        background-color: transparent;
        padding: 0;
        font-size: 100%;
        white-space: pre;
    }
    
    .rendered-markdown a {
        color: #0366d6;
        text-decoration: none;
    }
    
    .rendered-markdown a:hover {
        text-decoration: underline;
    }
    
    .rendered-markdown table {
        border-collapse: collapse;
        width: 100%;
        margin-bottom: 1em;
    }
    
    .rendered-markdown table th, .rendered-markdown table td {
        border: 1px solid #dfe2e5;
        padding: 6px 13px;
    }
    
    .rendered-markdown table th {
        background-color: #f6f8fa;
        font-weight: 600;
    }
    
    .rendered-markdown img {
        max-width: 100%;
        height: auto;
    }
    
    .rendered-markdown hr {
        height: 0.25em;
        padding: 0;
        margin: 24px 0;
        background-color: #e1e4e8;
        border: 0;
    }
    
    /* KaTeX math styles */
    .katex-display {
        display: block;
        margin: 1em 0;
        text-align: center;
        overflow-x: auto;
        overflow-y: hidden;
    }
    
    .katex-block-wrapper {
        margin: 1.5em 0;
    }
    
    .katex-error {
        color: #f44336;
        border: 1px solid #f44336;
        padding: 2px 5px;
        border-radius: 3px;
        font-family: monospace;
        background-color: #ffebee;
    }
    
    /* Code blocks and copy button */
    .copy-code-button {
        position: absolute;
        top: 5px;
        right: 5px;
        background: rgba(0, 0, 0, 0.1);
        color: #333;
        padding: 3px 8px;
        border: none;
        border-radius: 4px;
        font-size: 0.8em;
        cursor: pointer;
        opacity: 0;
        transition: opacity 0.3s;
    }
    
    pre:hover .copy-code-button {
        opacity: 1;
    }
    
    .copy-code-button:hover {
        background: rgba(0, 0, 0, 0.2);
    }
    
    /* Dark mode adjustments */
    @media (prefers-color-scheme: dark) {
        .rendered-markdown blockquote {
            border-left-color: #4a4a4a;
            color: #b5b5b5;
        }
        
        .rendered-markdown pre, .rendered-markdown table th {
            background-color: #2d2d2d;
        }
        
        .rendered-markdown code {
            background-color: rgba(200, 200, 200, 0.15);
        }
        
        .rendered-markdown h1, .rendered-markdown h2 {
            border-bottom-color: #4a4a4a;
        }
        
        .rendered-markdown table th, .rendered-markdown table td {
            border-color: #4a4a4a;
        }
        
        .rendered-markdown a {
            color: #58a6ff;
        }
        
        .rendered-markdown hr {
            background-color: #4a4a4a;
        }
        
        .katex-error {
            color: #ef9a9a;
            background-color: rgba(244, 67, 54, 0.1);
            border-color: #ef9a9a;
        }
        
        .copy-code-button {
            background: rgba(255, 255, 255, 0.1);
            color: #e0e0e0;
        }
        
        .copy-code-button:hover {
            background: rgba(255, 255, 255, 0.2);
        }
    }

    /* Mermaid diagram styles */
    .mermaid-container {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        margin: 1.5em 0 !important;
        overflow-x: auto !important;
        width: 100% !important;
        text-align: center !important;
        position: relative !important; /* 确保能正确定位下载按钮 */
    }
    
    .mermaid-diagram {
        width: 100% !important;
        min-height: 100px !important;
        max-width: 70% !important; /* 修改：从50%增加到70%的父容器宽度 */
        margin: 0 auto !important; /* 水平居中 */
        position: relative !important; /* 确保能正确定位下载按钮 */
    }
    
    .mermaid-buttons {
        position: absolute !important;
        top: 5px !important;
        right: 18% !important; /* 修改：根据新的图表宽度调整，从28%改为18%，保证按钮在图表内右上角 */
        z-index: 10 !important;
        display: flex !important;
        gap: 5px !important;
        opacity: 0 !important;
        transition: opacity 0.3s ease !important;
    }
    
    .mermaid-diagram:hover .mermaid-buttons,
    .mermaid-container:hover .mermaid-buttons {
        opacity: 1 !important;
    }
    
    .mermaid-download-button {
        background: rgba(255, 255, 255, 0.7) !important;
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
        padding: 4px !important;
        cursor: pointer !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 28px !important;
        height: 28px !important;
        transition: all 0.2s ease !important;
        color: #333 !important;
    }
    
    .mermaid-download-button:hover {
        background: rgba(230, 184, 77, 0.9) !important;
        border-color: #e6b84d !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }
    
    /* 响应式调整下载按钮位置 */
    @media (max-width: 768px) {
        .mermaid-buttons {
            right: 5% !important; /* 在移动设备上调整按钮位置 */
        }
    }
    
    .mermaid-error-container {
        border: 1px solid #f44336 !important;
        border-radius: 4px !important;
        color: #f44336 !important;
        background-color: #ffebee !important;
        padding: 10px !important;
        margin: 10px 0 !important;
        font-family: monospace !important;
        white-space: pre-wrap !important;
        font-size: 0.9em !important;
        max-height: 200px !important;
        overflow-y: auto !important;
        text-align: left !important;
    }
    
    .diagram-loading {
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        height: 100px !important;
        color: #666 !important;
        font-style: italic !important;
    }
    
    /* 新增：Mermaid内LaTeX数学公式样式 */
    .mermaid-math-formula {
        display: inline-block !important;
        vertical-align: middle !important;
        margin: 0 3px !important;
        padding: 2px !important;
        background-color: rgba(255, 255, 255, 0.8) !important;
        border-radius: 3px !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    }
    
    .mermaid-math-formula .katex {
        font-size: 1.1em !important;
    }
    
    /* 修正SVG文本中数学公式位置 */
    .mermaid-diagram text tspan.mermaid-math-formula {
        dominant-baseline: middle !important;
    }
    
    /* Responsive adjustments for diagrams */
    @media (max-width: 768px) {
        .mermaid-diagram {
            max-width: 95% !important; /* 在移动设备上放宽宽度限制 */
        }
        
        .mermaid-diagram svg {
            max-height: 400px !important;
        }
        
        .mermaid-diagram text {
            font-size: 12px !important;
        }
    }

    /* 这些样式已经在上面统一定义了，这里删除重复定义 */
    
    /* 这些样式已经在上面统一定义了，这里删除重复定义 */
    
    .delete-button {
        background-color: #e67474 !important;
        color: white !important;
        border: none !important;
        border-radius: 6px !important;
        padding: 5px 10px !important;
        cursor: pointer !important;
        transition: background-color 0.3s !important;
    }
    
    .delete-button:hover {
        background-color: #d85555 !important;
    }
    
    /* 响应式设计增强 */
    @media (max-width: 768px) {
        .chat-header h1 {
            font-size: 1.2rem;
        }
        
        .system-message, .you-message, .ai-message {
            max-width: 85%;
        }
        
        .settings-item {
            margin-right: 10px;
            width: calc(50% - 20px);
        }
        
        #file-selection-container {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        }
        
        .chat-history {
            padding: 10px;
            margin: 5px;
        }
        
        #userInput {
            padding: 10px;
        }
        
        #sendButton, #newChatButton, #downloadButton {
            padding: 0 15px;
            height: 42px;
        }
    }
    
    @media (max-width: 480px) {
        body, html {
            font-size: 15px;
        }
        
        .chat-header {
            padding: 8px 12px;
        }
        
        .logo {
            height: 24px;
        }
        
        .chat-history {
            padding: 8px;
            margin: 3px;
        }
        
        .system-message, .you-message, .ai-message {
            max-width: 95%;
            padding: 10px 12px;
        }
        
        #sendButton, #newChatButton, #downloadButton {
            padding: 0 10px;
            margin-left: 5px;
            font-size: 0.9em;
        }
        
        .settings-item {
            width: 100%;
            margin-right: 0;
        }
        
        #file-selection-container {
            grid-template-columns: 1fr;
            padding: 10px !important;
        }
        
        .chat-input {
            padding: 8px;
        }
        
        .message-content {
            font-size: 0.95em;
        }
    }

    /* 按钮通用样式增强 */
    .btn {
        padding: 8px 16px !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        border: none !important;
        outline: none !important;
        cursor: pointer !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }
    
    .btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    }
    
    .btn:active {
        transform: translateY(1px) !important;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    }
    
    /* 这些样式已经在上面统一定义了，这里删除重复定义 */
    
    /* 添加按钮图标空间 */
    #sendButton, #newChatButton, #downloadButton {
        padding: 0 16px !important;
        position: relative !important;
    }
    
    /* 微妙下划线动效 */
    .you-message::after, .ai-message::after {
        content: '' !important;
        position: absolute !important;
        bottom: 0 !important;
        left: 50% !important;
        width: 0 !important;
        height: 2px !important;
        background: rgba(230, 184, 77, 0.5) !important;
        transition: width 0.3s ease, left 0.3s ease !important;
        transform: translateX(-50%) !important;
    }
    
    .you-message:hover::after, .ai-message:hover::after {
        width: 100% !important;
    }
    
    /* 输入框聚焦效果增强 */
    #userInput:focus {
        box-shadow: 0 0 0 4px rgba(66, 153, 225, 0.2), 0 8px 25px rgba(66, 153, 225, 0.15) !important;
        border-color: rgba(66, 153, 225, 0.4) !important;
        outline: none !important;
        transform: translateY(-2px) scale(1.01) !important;
    }

    /* 现代化悬停效果 */
    .hover-lift {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    .hover-lift:hover {
        transform: translateY(-4px) scale(1.02) !important;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1) !important;
    }

    /* 现代化点击效果 */
    .click-effect {
        position: relative !important;
        overflow: hidden !important;
    }

    .click-effect::after {
        content: '' !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        width: 5px !important;
        height: 5px !important;
        background: rgba(255, 255, 255, 0.5) !important;
        opacity: 0 !important;
        border-radius: 100% !important;
        transform: scale(1, 1) translate(-50%) !important;
        transform-origin: 50% 50% !important;
    }

    .click-effect:focus:not(:active)::after {
        animation: ripple 1s ease-out !important;
    }

    @keyframes ripple {
        0% {
            transform: scale(0, 0);
            opacity: 1;
        }
        20% {
            transform: scale(25, 25);
            opacity: 1;
        }
        100% {
            opacity: 0;
            transform: scale(40, 40);
        }
    }

    /* Message content with markdown rendering */
    .message-content.rendered-markdown {
        white-space: normal !important; /* Override pre-wrap for better markdown display */
    }

    /* 这些样式已经在上面统一定义了，这里删除重复定义 */

    .toggle-button:before {
        content: "" !important;
        display: inline-block !important;
        width: 18px !important;
        height: 18px !important;
        margin-right: 8px !important;
        background-repeat: no-repeat !important;
        background-position: center !important;
        background-size: contain !important;
        transition: transform 0.3s ease !important;
    }

    .toggle-button-collapsed:before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z'/%3E%3C/svg%3E") !important;
        transform: rotate(-90deg) !important;
    }

    .toggle-button-expanded:before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z'/%3E%3C/svg%3E") !important;
        transform: rotate(0deg) !important;
    }

    .toggle-button:after {
        content: "" !important;
        position: absolute !important;
        width: 100% !important;
        height: 100% !important;
        top: 0 !important;
        left: -100% !important;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent) !important;
        transition: 0.5s ease !important;
    }

    .toggle-button:hover:after {
        left: 100% !important;
    }

    .toggle-button-expanded {
        background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%) !important;
        border-bottom-left-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        margin-bottom: -1px !important;
        position: relative !important;
        z-index: 5 !important;
    }

    .toggle-button-collapsed {
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
        border-radius: 12px !important;
    }

    /* 优化选项卡标题区域的布局 */
    div[style*="display: flex; justify-content: space-between;"] {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin: 15px 10px 0 !important;
        padding: 0 5px !important;
        position: relative !important;
    }

    /* 文件选择容器过渡效果 - 统一风格 */
    #file-selection-container {
        border: 1px solid rgba(66, 153, 225, 0.2) !important;
        border-radius: 0 16px 16px 16px !important; /* 左上角不要圆角，与按钮衔接 */
        padding: 20px !important;
        background: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%) !important;
        margin: 0 16px 16px 16px !important; /* 上边距减少，与按钮衔接 */
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04) !important;
        max-height: 300px !important;
        overflow-y: auto !important;
        transition: all 0.3s ease-in-out !important;
        position: relative !important;
        z-index: 4 !important;
        backdrop-filter: blur(10px) !important;
    }

    /* 为手机版添加样式 */
    @media (max-width: 768px) {
        .toggle-button {
            min-width: 120px !important;
            padding: 8px 12px !important;
            font-size: 0.9rem !important;
        }
        
        .toggle-button:before {
            width: 16px !important;
            height: 16px !important;
            margin-right: 6px !important;
        }
    }

    /* 添加消息发送者样式 */
    .message-sender {
        font-weight: 600 !important;
        margin-bottom: 8px !important;
        font-size: 1.05rem !important;
        color: #8B4513 !important;
        display: flex !important;
        align-items: center !important;
    }
    
    /* 为emoji添加特殊样式 */
    .message-sender::first-letter {
        margin-right: 4px !important;
        font-size: 1.2rem !important;
    }
    
    /* 用户消息发送者样式 */
    .you-message .message-sender {
        color: #8B4513 !important;
    }
    
    /* AI消息发送者样式 */
    .ai-message .message-sender {
        color: #2A6B9C !important;
    }
    
    /* 系统消息发送者样式 */
    .system-message .message-sender {
        color: #5A5A5A !important;
    }

    /* 发送者emoji样式 */
    .sender-emoji {
        font-size: 1.2rem !important;
        margin-right: 6px !important;
        display: inline-block !important;
        animation: bounceSender 0.5s ease !important;
    }
    
    /* 发送者emoji动画 */
    @keyframes bounceSender {
        0% { transform: scale(0); opacity: 0; }
        50% { transform: scale(1.2); opacity: 0.7; }
        100% { transform: scale(1); opacity: 1; }
    }

    /* 现代化加载动画 */
    .loading-spinner {
        display: inline-block !important;
        width: 20px !important;
        height: 20px !important;
        border: 2px solid rgba(255, 255, 255, 0.3) !important;
        border-radius: 50% !important;
        border-top-color: #fff !important;
        animation: spin 1s ease-in-out infinite !important;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* 现代化脉冲动画 */
    .pulse-animation {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite !important;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    /* 现代化淡入动画 */
    .fade-in {
        animation: fadeIn 0.5s ease-out !important;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* 现代化滑入动画 */
    .slide-in-left {
        animation: slideInLeft 0.5s ease-out !important;
    }

    .slide-in-right {
        animation: slideInRight 0.5s ease-out !important;
    }

    @keyframes slideInLeft {
        from { opacity: 0; transform: translateX(-50px); }
        to { opacity: 1; transform: translateX(0); }
    }

    @keyframes slideInRight {
        from { opacity: 0; transform: translateX(50px); }
        to { opacity: 1; transform: translateX(0); }
    }

    /* 现代化弹跳动画 */
    .bounce-in {
        animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
    }

    @keyframes bounceIn {
        0% { opacity: 0; transform: scale(0.3); }
        50% { opacity: 1; transform: scale(1.05); }
        70% { transform: scale(0.9); }
        100% { opacity: 1; transform: scale(1); }
    }

    /* 现代化暗色模式支持 */
    @media (prefers-color-scheme: dark) {
        body, html {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%) !important;
            color: #e2e8f0 !important;
        }

        .chat-container {
            background: rgba(26, 32, 44, 0.95) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        #chatHistory {
            background: linear-gradient(145deg, rgba(26, 32, 44, 0.8) 0%, rgba(45, 55, 72, 0.8) 100%) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        .you-message {
            background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%) !important;
        }

        .ai-message {
            background: linear-gradient(135deg, #38a169 0%, #2f855a 100%) !important;
        }

        .system-message {
            background: linear-gradient(135deg, #dd6b20 0%, #c05621 100%) !important;
        }

        .chat-input {
            background: rgba(26, 32, 44, 0.9) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        #userInput {
            background: rgba(45, 55, 72, 0.8) !important;
            color: #e2e8f0 !important;
            border: 2px solid rgba(255, 255, 255, 0.1) !important;
        }

        .file-item {
            background: linear-gradient(145deg, rgba(26, 32, 44, 0.8) 0%, rgba(45, 55, 72, 0.8) 100%) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            color: #e2e8f0 !important;
        }

        .options-container {
            background: linear-gradient(145deg, rgba(26, 32, 44, 0.8) 0%, rgba(45, 55, 72, 0.8) 100%) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        .options-container > div {
            background: linear-gradient(145deg, rgba(45, 55, 72, 0.8) 0%, rgba(74, 85, 104, 0.8) 100%) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        .settings-item {
            background: linear-gradient(145deg, rgba(45, 55, 72, 0.8) 0%, rgba(74, 85, 104, 0.8) 100%) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        .settings-item label {
            color: #e2e8f0 !important;
        }

        .settings-item select, .settings-item input[type="number"] {
            background: rgba(26, 32, 44, 0.8) !important;
            color: #e2e8f0 !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
        }
    }

    /* 现代化可访问性增强 */
    @media (prefers-reduced-motion: reduce) {
        *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }

        .chat-container {
            animation: none !important;
        }

        body, html {
            animation: none !important;
        }
    }

    /* 高对比度模式支持 */
    @media (prefers-contrast: high) {
        .you-message {
            background: #000080 !important;
            border: 2px solid #ffffff !important;
        }

        .ai-message {
            background: #800080 !important;
            border: 2px solid #ffffff !important;
        }

        .system-message {
            background: #008080 !important;
            border: 2px solid #ffffff !important;
        }

        #selectAllButton, .submit-button, .toggle-button,
        #sendButton, #newChatButton, #downloadButton {
            background: #000000 !important;
            border: 2px solid #ffffff !important;
        }
    }

    /* 现代化焦点指示器 */
    *:focus {
        outline: 2px solid #4299e1 !important;
        outline-offset: 2px !important;
    }

    /* 现代化选择样式 */
    ::selection {
        background: rgba(66, 153, 225, 0.3) !important;
        color: inherit !important;
    }

    ::-moz-selection {
        background: rgba(66, 153, 225, 0.3) !important;
        color: inherit !important;
    }

    /* 现代化触摸设备优化 */
    @media (hover: none) and (pointer: coarse) {
        .you-message:hover, .ai-message:hover, .system-message:hover {
            transform: none !important;
        }

        .file-item:hover {
            transform: none !important;
        }

        #selectAllButton:hover, .submit-button:hover, .toggle-button:hover,
        #sendButton:hover, #newChatButton:hover, #downloadButton:hover {
            transform: none !important;
        }

        /* 增加触摸目标大小 */
        #sendButton, #newChatButton, #downloadButton {
            min-height: 48px !important;
            min-width: 48px !important;
        }

        .file-item {
            min-height: 48px !important;
        }

        .toggle-button {
            min-height: 48px !important;
        }
    }

    /* 聊天限制信息样式 */
    .chat-info-container {
        position: fixed !important;
        top: 20px !important;
        right: 20px !important;
        z-index: 1000 !important;
        background: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%) !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05) !important;
        border: 1px solid rgba(66, 153, 225, 0.2) !important;
        backdrop-filter: blur(10px) !important;
        max-width: 300px !important;
    }

    .chat-info-trigger {
        padding: 12px 16px !important;
        cursor: pointer !important;
        color: #4a5568 !important;
        font-weight: 500 !important;
        font-size: 0.9rem !important;
        transition: all 0.3s ease !important;
        border-radius: 12px !important;
    }

    .chat-info-trigger:hover {
        background: linear-gradient(135deg, rgba(66, 153, 225, 0.05) 0%, rgba(49, 130, 206, 0.05) 100%) !important;
        color: #4299e1 !important;
    }

    .chat-limits {
        padding: 16px !important;
        border-top: 1px solid rgba(66, 153, 225, 0.1) !important;
        display: none !important;
    }

    .chat-limits.show {
        display: block !important;
        animation: slideDown 0.3s ease-out !important;
    }

    .limit-item {
        color: #2d3748 !important;
        font-size: 0.85rem !important;
        line-height: 1.6 !important;
    }

    .limit-icon {
        margin-right: 8px !important;
    }

    .remaining-count {
        display: inline-block !important;
        margin-top: 8px !important;
        padding: 8px 12px !important;
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
        color: white !important;
        border-radius: 8px !important;
        font-weight: 600 !important;
        font-size: 0.85rem !important;
        box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3) !important;
    }

    .count-icon {
        margin-right: 6px !important;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/contrib/auto-render.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.5.1/highlight.min.js"></script> 
    <!-- 添加内联Markdown渲染器代码 -->
    <script>
        // markdown-renderer.js - Markdown rendering functionality
        // This module provides functions for rendering markdown with KaTeX and syntax highlighting

        /**
         * Initialize the markdown renderer with required dependencies
         * @param {Object} dependencies - Object containing required libraries
         * @returns {Object} - The markdown renderer object with rendering functions
         */
        function initMarkdownRenderer(dependencies = {}) {
          // Extract dependencies or use defaults if available in global scope
          const markdownit = dependencies.markdownit || window.markdownit;
          const hljs = dependencies.hljs || window.hljs;
          const katex = dependencies.katex || window.katex;
          const mermaid = dependencies.mermaid || window.mermaid;
          
          // Verify required dependencies
          if (!markdownit) {
            console.error("markdown-it library is required but not available");
            return null;
          }

          // Initialize mermaid if available
          if (mermaid) {
            try {
              // Configure mermaid
              mermaid.initialize({
                startOnLoad: false,
                theme: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'default',
                logLevel: 3, // Error level to reduce console noise
                securityLevel: 'loose',
                fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                fontSize: 14,
                flowchart: {
                  htmlLabels: true,
                  curve: 'basis'
                },
                sequence: {
                  diagramMarginX: 50,
                  diagramMarginY: 10,
                  actorMargin: 50,
                  width: 150,
                  height: 65,
                  boxMargin: 10,
                  messageMargin: 35
                }
              });
              console.log("Mermaid initialized successfully");
            } catch (err) {
              console.error("Failed to initialize Mermaid:", err);
            }
          } else {
            console.warn("Mermaid library not available, diagram rendering will be disabled");
          }

          // Create a markdown-it instance
          const md = markdownit({
            html: true,
            breaks: false,
            linkify: true,
            xhtmlOut: true,
            typographer: true,
            highlight: function(str, lang) {
              // Handle code syntax highlighting if hljs is available
              try {
                if (lang && hljs && hljs.getLanguage && hljs.getLanguage(lang)) {
                  try {
                    return `<pre class="hljs"><code class="hljs language-${lang}">${hljs.highlight(str, {language: lang, ignoreIllegals: true}).value}</code></pre>`;
                  } catch (err) {
                    console.error('Highlight.js error:', err);
                  }
                }
              } catch (e) {
                console.warn('Error with syntax highlighting:', e);
              }
              
              // Fallback rendering without syntax highlighting
              return `<pre class="hljs"><code class="hljs">${md.utils.escapeHtml(str)}</code></pre>`;
            }
          });

          // Helper function to safely use plugins
          function safeUsePlugin(md, plugin, ...args) {
            try {
              if (typeof plugin === 'function') {
                md.use(plugin, ...args);
              }
            } catch (err) {
              console.warn(`Failed to load plugin:`, err);
            }
          }
          
          // 添加自定义表格规则，支持无需空行的表格渲染
          md.use(function(md) {
            // 获取原始解析函数
            const tableRule = md.block.ruler.__rules__.find(rule => rule.name === 'table');
            const originalFn = tableRule ? tableRule.fn : null;
            
            if (originalFn) {
              // 替换为更宽松的实现
              md.block.ruler.at('table', function tableWithoutNewlines(state, startLine, endLine, silent) {
                // 直接实现无需空行的表格
                const startPos = state.bMarks[startLine] + state.tShift[startLine];
                const endPos = state.eMarks[startLine];
                const lineText = state.src.slice(startPos, endPos);
                
                // 快速检查这是否看起来像表格
                if (lineText.indexOf('|') === -1) {
                  return false;
                }
                
                // 检查是否有表头行和分隔行
                if (startLine + 1 >= endLine) {
                  return false;
                }
                
                // 检查分隔行（第二行）
                const sepLineStart = state.bMarks[startLine + 1] + state.tShift[startLine + 1];
                const sepLineEnd = state.eMarks[startLine + 1];
                const sepLine = state.src.slice(sepLineStart, sepLineEnd);
                
                // 确保它有 | 和 - 字符（表格分隔符）
                if (sepLine.indexOf('|') === -1 || sepLine.indexOf('-') === -1) {
                  return false;
                }
                
                // 验证这确实是表格分隔行
                const sepParts = sepLine.split('|');
                let validSeparator = true;
                
                for (let i = 0; i < sepParts.length; i++) {
                  const trimmed = sepParts[i].trim();
                  // 分隔符中的每个单元格必须至少包含一个破折号
                  if (trimmed.length > 0 && !/-+/.test(trimmed)) {
                    validSeparator = false;
                    break;
                  }
                }
                
                if (!validSeparator) {
                  return false;
                }
                
                // 此时我们确信这是一个表格
                if (silent) {
                  return true;
                }
                
                // 处理表格头部
                const headerParts = lineText.split('|');
                const columnCount = headerParts.length;
                
                // 创建表格开始标记
                let token = state.push('table_open', 'table', 1);
                token.map = [startLine, 0]; // 在表格关闭时更新
                
                // 创建thead开始标记
                token = state.push('thead_open', 'thead', 1);
                token.map = [startLine, startLine + 1];
                
                // 创建tr开始标记
                token = state.push('tr_open', 'tr', 1);
                token.map = [startLine, startLine + 1];
                
                // 处理表头单元格
                for (let i = 0; i < headerParts.length; i++) {
                  let cellContent = headerParts[i].trim();
                  if (cellContent.length > 0 || (i > 0 && i < headerParts.length - 1)) {
                    token = state.push('th_open', 'th', 1);
                    
                    // 从分隔行确定对齐方式
                    if (i < sepParts.length) {
                      const sepCell = sepParts[i].trim();
                      let align = '';
                      
                      if (sepCell.startsWith(':') && sepCell.endsWith(':')) {
                        align = 'center';
                      } else if (sepCell.startsWith(':')) {
                        align = 'left';
                      } else if (sepCell.endsWith(':')) {
                        align = 'right';
                      }
                      
                      if (align) {
                        token.attrs = [['style', `text-align:${align}`]];
                      }
                    }
                    
                    // 使用内联规则处理单元格内容
                    token = state.push('inline', '', 0);
                    token.content = cellContent;
                    token.map = [startLine, startLine + 1];
                    token.children = [];
                    
                    token = state.push('th_close', 'th', -1);
                  }
                }
                
                token = state.push('tr_close', 'tr', -1);
                token = state.push('thead_close', 'thead', -1);
                
                // 创建tbody
                token = state.push('tbody_open', 'tbody', 1);
                token.map = [startLine + 2, 0]; // 在tbody关闭时更新
                
                // 处理表格主体行
                let rowLine = startLine + 2;
                let tableOpen = true;
                
                while (rowLine < endLine) {
                  const rowStart = state.bMarks[rowLine] + state.tShift[rowLine];
                  const rowEnd = state.eMarks[rowLine];
                  const rowText = state.src.slice(rowStart, rowEnd);
                  
                  // 如果行不像表格行（没有管道符），则退出
                  if (rowText.indexOf('|') === -1) {
                    break;
                  }
                  
                  // 创建行
                  token = state.push('tr_open', 'tr', 1);
                  token.map = [rowLine, rowLine + 1];
                  
                  const rowParts = rowText.split('|');
                  
                  // 处理单元格
                  for (let i = 0; i < rowParts.length; i++) {
                    let cellContent = rowParts[i].trim();
                    if (cellContent.length > 0 || (i > 0 && i < rowParts.length - 1)) {
                      token = state.push('td_open', 'td', 1);
                      
                      // 应用表头中的对齐方式
                      if (i < sepParts.length) {
                        const sepCell = sepParts[i].trim();
                        let align = '';
                        
                        if (sepCell.startsWith(':') && sepCell.endsWith(':')) {
                          align = 'center';
                        } else if (sepCell.startsWith(':')) {
                          align = 'left';
                        } else if (sepCell.endsWith(':')) {
                          align = 'right';
                        }
                        
                        if (align) {
                          token.attrs = [['style', `text-align:${align}`]];
                        }
                      }
                      
                      // 处理单元格内容
                      token = state.push('inline', '', 0);
                      token.content = cellContent;
                      token.map = [rowLine, rowLine + 1];
                      token.children = [];
                      
                      token = state.push('td_close', 'td', -1);
                    }
                  }
                  
                  token = state.push('tr_close', 'tr', -1);
                  rowLine++;
                }
                
                token = state.push('tbody_close', 'tbody', -1);
                token = state.push('table_close', 'table', -1);
                
                // 更新表格映射以包含所有行 - 安全地查找要更新的标记
                // 查找table_open和tbody_open标记并更新其映射
                for (let i = state.tokens.length - 1; i >= 0; i--) {
                  if (state.tokens[i].type === 'table_open' && state.tokens[i].map) {
                    state.tokens[i].map[1] = rowLine;
                  }
                  if (state.tokens[i].type === 'tbody_open' && state.tokens[i].map) {
                    state.tokens[i].map[1] = rowLine;
                  }
                  // 只查看此表格的最近标记
                  if (i < state.tokens.length - 30) break;
                }
                
                state.line = rowLine;
                return true;
              }, { alt: ['paragraph', 'reference'] });
            }
          });

          // Add KaTeX math rendering plugin
          if (katex) {
            md.use(function(md) {
              // Define delimiters
              const inlineDelimiter = '$';
              const blockDelimiter = '$$';
              
              // Helper to check if a string contains Chinese characters
              function containsChinese(str) {
                return /[\u4e00-\u9fff]/.test(str);
              }
              
              // Helper to auto-wrap Chinese characters in \text{} for KaTeX
              function wrapChineseWithText(content) {
                // Replace any Chinese character sequences with \text{...}
                return content.replace(/([\u4e00-\u9fff，：；？！（）]+)/g, '\\text{$1}');
              }
              
              // Inline math rule
              function mathInline(state, silent) {
                // Skip if not starting with $
                if (state.src[state.pos] !== inlineDelimiter) {
                  return false;
                }
                
                // Don't parse $ as math if it's part of a string like $100
                const prevChar = state.pos > 0 ? state.src[state.pos - 1] : null;
                if (prevChar && /[0-9a-zA-Z]/.test(prevChar)) {
                  return false;
                }
                
                // Look for the closing $
                let pos = state.pos + 1;
                let found = false;
                
                while (pos < state.posMax) {
                  if (state.src[pos] === inlineDelimiter) {
                    // Don't count $ if it's preceded by a backslash (escape)
                    if (state.src[pos - 1] !== '\\') {
                      // Make sure this isn't part of $$
                      if (state.src[pos + 1] !== inlineDelimiter) {
                        found = true;
                        break;
                      }
                    }
                  }
                  pos++;
                }
                
                // No closing delimiter found
                if (!found) {
                  if (!silent) {
                    state.pending += inlineDelimiter;
                  }
                  state.pos += 1;
                  return true;
                }
                
                // Don't allow empty math expressions
                if (pos === state.pos + 1) {
                  if (!silent) {
                    state.pending += inlineDelimiter + inlineDelimiter;
                  }
                  state.pos += 2;
                  return true;
                }
                
                if (!silent) {
                  const mathContent = state.src.slice(state.pos + 1, pos);
                  
                  const token = state.push('math_inline', 'math', 0);
                  token.content = mathContent;
                  token.markup = inlineDelimiter;
                }
                
                state.pos = pos + 1;
                return true;
              }
              
              // Block math rule
              function mathBlock(state, startLine, endLine, silent) {
                let pos = state.bMarks[startLine] + state.tShift[startLine];
                let max = state.eMarks[startLine];

                // Check if line starts with $$ (allowing for spaces handled by tShift)
                if (state.src.slice(pos, pos + 2) !== blockDelimiter) {
                  return false;
                }

                if (silent) {
                  return true;
                }

                // Search for the end $$ on subsequent lines
                let nextLine = startLine;
                let found = false;
                let content = '';

                // Check if the formula is on a single line: $$ formula $$
                const singleLineMatch = state.src.slice(pos + 2, max).match(/(.*?)\$\$\s*$/);
                if (singleLineMatch) {
                  content = singleLineMatch[1];
                  found = true;
                  nextLine = startLine; // Formula ends on the same line
                } else {
                  // Multi-line formula
                  content = state.src.slice(pos + 2, max); // Content from the first line
                  for (nextLine = startLine + 1; nextLine < endLine; nextLine++) {
                    if (state.tShift[nextLine] < state.blkIndent) { break; } // Line is less indented, not part of the block
                    
                    let lineStart = state.bMarks[nextLine] + state.tShift[nextLine];
                    let lineMax = state.eMarks[nextLine];
                    let lineText = state.src.slice(lineStart, lineMax);

                    if (lineText.endsWith(blockDelimiter)) {
                      content += '\n' + lineText.slice(0, -2); // Add content before $$, removing $$ itself
                      found = true;
                      break;
                    }
                    content += '\n' + lineText;
                  }
                }

                if (!found) {
                  return false; // No closing delimiter found
                }

                state.line = nextLine + 1;

                const token = state.push('math_block', 'math', 0);
                token.block = true;
                token.content = content.trim();
                token.markup = blockDelimiter;
                token.map = [startLine, state.line];

                return true;
              }
              
              // Register our rules
              md.inline.ruler.after('escape', 'math_inline', mathInline);
              md.block.ruler.before('fence', 'math_block', mathBlock);
              
              // Render inline math
              md.renderer.rules.math_inline = function(tokens, idx) {
                try {
                  // Process content to handle Chinese characters
                  let content = tokens[idx].content;
                  if (containsChinese(content)) {
                    content = wrapChineseWithText(content);
                  }
                  
                  return katex.renderToString(content, { 
                    displayMode: false, 
                    throwOnError: false,
                    strict: false,
                    trust: true,
                    macros: {
                      "\\RR": "\\mathbb{R}"
                    }
                  });
                } catch (err) {
                  console.error("KaTeX error (inline):", err);
                  return `<span class="katex-error" title="${err}">${md.utils.escapeHtml(tokens[idx].content)}</span>`;
                }
              };
              
              // Render block math
              md.renderer.rules.math_block = function(tokens, idx) {
                try {
                  // Process content to handle Chinese characters
                  let content = tokens[idx].content;
                  if (containsChinese(content)) {
                    content = wrapChineseWithText(content);
                  }
                  
                  // Always use displayMode: true for block math
                  return `<div class="katex-block-wrapper"><div class="katex-display">${katex.renderToString(content, { 
                    displayMode: true, 
                    throwOnError: false,
                    strict: false,
                    trust: true,
                    macros: {
                      "\\RR": "\\mathbb{R}"
                    }
                  })}</div></div>`;
                } catch (err) {
                  console.error("KaTeX error (block):", err, tokens[idx].content);
                  return `<div class="katex-display katex-error" title="${err}">${md.utils.escapeHtml(tokens[idx].content)}</div>`;
                }
              };
            });
          }

          // Pre-process block math formulas to ensure they have proper spacing
          function preprocessBlockMath(markdown) {
            // 首先识别表格区域，避免在表格中添加不必要的空行
            const tableRegex = /^\s*(\|[^\n]*\|\s*\n\s*\|[\-\|:\s]*\|\s*\n[\s\S]*?(?=\n\s*\n|\n\s*$|$))/gm;
            const tableMatches = [];
            let tableMatch;
            
            // 收集所有表格匹配
            while ((tableMatch = tableRegex.exec(markdown)) !== null) {
              tableMatches.push({
                start: tableMatch.index,
                end: tableMatch.index + tableMatch[0].length,
                content: tableMatch[0]
              });
            }
            
            // 为数学表达式预处理，但避开表格区域
            let processed = markdown;
            const mathRegex = /([^\$]*)(\$\$[\s\S]*?\$\$)([^\$]*)/g;
            let mathMatches = [];
            let mathMatch;
            
            // 收集所有数学表达式匹配
            while ((mathMatch = mathRegex.exec(markdown)) !== null) {
              mathMatches.push({
                start: mathMatch.index,
                end: mathMatch.index + mathMatch[0].length,
                match: mathMatch
              });
            }
            
            // 倒序处理数学表达式，以避免修改后的索引问题
            mathMatches.reverse().forEach(m => {
              // 检查这个数学表达式是否在表格内
              const isInTable = tableMatches.some(table => 
                (m.start >= table.start && m.start < table.end) ||
                (m.end > table.start && m.end <= table.end)
              );
              
              // 如果不在表格内，正常处理
              if (!isInTable) {
                const [match, textBefore, mathBlock, textAfter] = [m.match[0], m.match[1], m.match[2], m.match[3]];
                
                // Trim the captured surrounding text
                const trimmedBefore = textBefore ? textBefore.trim() : '';
                const trimmedAfter = textAfter ? textAfter.trim() : '';
                
                // Ensure mathBlock has separate lines for delimiters and content
                const internalContent = mathBlock.slice(2, -2).trim(); // Get content between $$
                const correctlyFormattedMathBlock = '$$\n' + internalContent + '\n$$';
                
                let result = '';
                if (trimmedBefore) {
                  result += trimmedBefore + '\n\n'; // Add double newline after textBefore
                }
                result += correctlyFormattedMathBlock;
                if (trimmedAfter) {
                  result += '\n\n' + trimmedAfter; // Add double newline before textAfter
                }
                
                // 替换片段
                processed = processed.substring(0, m.start) + result + processed.substring(m.end);
              }
            });
            
            // 折叠多于两个的换行为两个，但避开表格区域
            let result = '';
            let lastIndex = 0;
            
            // 对于每个表格区域，处理表格前的内容，然后保留表格内容不变
            tableMatches.forEach(table => {
              // 处理表格前的内容
              if (table.start > lastIndex) {
                const beforeTable = processed.substring(lastIndex, table.start);
                result += beforeTable.replace(/\n{3,}/g, '\n\n');
              }
              
              // 添加原始表格内容，不做修改
              result += processed.substring(table.start, table.end);
              lastIndex = table.end;
            });
            
            // 处理最后一个表格后的内容
            if (lastIndex < processed.length) {
              const afterLastTable = processed.substring(lastIndex);
              result += afterLastTable.replace(/\n{3,}/g, '\n\n');
            }
            
            return result || processed; // 如果result为空，返回原始处理内容
          }

          /**
           * Render markdown to HTML
           * @param {string} markdown - The markdown text to render
           * @returns {string} - The rendered HTML
           */
          function renderMarkdown(markdown) {
            // Pre-process block math formulas
            const processedMarkdown = preprocessBlockMath(markdown);
            
            // Render the markdown to HTML
            const renderedHtml = md.render(processedMarkdown);
            
            return renderedHtml;
          }

          /**
           * Process rendered HTML content in a DOM container
           * @param {HTMLElement} container - The container element to process
           */
          function processRenderedContent(container) {
            // Apply syntax highlighting to code blocks if available
            if (hljs) {
              try {
                container.querySelectorAll('pre code').forEach(block => {
                  hljs.highlightElement(block);
                });
              } catch (err) {
                console.error('Error applying syntax highlighting:', err);
              }
            }
            
            // Add copy button to code blocks
            container.querySelectorAll('pre').forEach(block => {
              // Only add if there isn't already a copy button
              if (!block.querySelector('.copy-code-button')) {
                addCopyButtonToCodeBlock(block);
              }
            });
            
            // Process Mermaid diagrams if available
            if (mermaid) {
              try {
                // Look for any non-rendered mermaid diagrams (code blocks with class 'language-mermaid')
                container.querySelectorAll('pre code.language-mermaid').forEach(block => {
                  const code = block.textContent;
                  if (code) {
                    // Create a unique ID for this diagram
                    const diagramId = `mermaid-diagram-${Date.now()}-${Math.floor(Math.random() * 100000)}`;
                    
                    // Create container
                    const diagramContainer = document.createElement('div');
                    diagramContainer.className = 'mermaid-container';
                    diagramContainer.innerHTML = `
                      <div id="${diagramId}" class="mermaid-diagram" data-diagram-code="${code.replace(/"/g, '&quot;')}">
                        <div class="diagram-loading">Loading diagram...</div>
                      </div>
                    `;
                    
                    // Replace the code block with the diagram container
                    const preElement = block.parentNode;
                    preElement.parentNode.replaceChild(diagramContainer, preElement);
                    
                    // Process the diagram
                    setTimeout(() => {
                      processMermaid(diagramId, code);
                    }, 10);
                  }
                });
              } catch (err) {
                console.error('Error processing Mermaid diagrams:', err);
              }
            }
            
            // Render KaTeX formulas that might be in the content
            if (katex && typeof renderMathInElement === 'function') {
              try {
                renderMathInElement(container, {
                  delimiters: [
                    {left: "$$", right: "$$", display: true},
                    {left: "$", right: "$", display: false}
                  ],
                  throwOnError: false
                });
              } catch (err) {
                console.error('Error rendering KaTeX formulas:', err);
              }
            }
          }
          
          // For code blocks add copy button
          function addCopyButtonToCodeBlock(block) {
            // Create copy button
            const copyButton = document.createElement('button');
            copyButton.className = 'copy-code-button';
            copyButton.textContent = '复制';
            
            // Add copy functionality
            copyButton.addEventListener('click', function() {
              const code = block.querySelector('code');
              if (code) {
                const text = code.textContent;
                navigator.clipboard.writeText(text).then(
                  function() {
                    // Temporary change button text to indicate success
                    copyButton.textContent = '已复制!';
                    setTimeout(function() {
                      copyButton.textContent = '复制';
                    }, 2000);
                  },
                  function(err) {
                    console.error('无法复制文本: ', err);
                    copyButton.textContent = '复制失败';
                    setTimeout(function() {
                      copyButton.textContent = '复制';
                    }, 2000);
                  }
                );
              }
            });
            
            // Add button to code block
            block.style.position = 'relative';
            block.appendChild(copyButton);
          }

          // Add Mermaid diagram support
          if (mermaid) {
            md.use(function(md) {
              const defaultFence = md.renderer.rules.fence.bind(md.renderer.rules);
              
              md.renderer.rules.fence = function(tokens, idx, options, env, slf) {
                const token = tokens[idx];
                const code = token.content.trim();
                const langName = token.info.trim().toLowerCase();
                
                // Handle mermaid code blocks
                if (langName === 'mermaid') {
                  // Create unique ID for this diagram
                  const diagramId = `mermaid-diagram-${Date.now()}-${Math.floor(Math.random() * 100000)}`;
                  
                  // Create container with loading indicator
                  setTimeout(function() {
                    processMermaid(diagramId, code);
                  }, 10);
                  
                  // Return container with loading state
                  return `
                    <div class="mermaid-container">
                      <div id="${diagramId}" class="mermaid-diagram" data-diagram-code="${md.utils.escapeHtml(code)}">
                        <div class="diagram-loading">Loading diagram...</div>
                      </div>
                    </div>
                  `;
                }
                
                // Default rendering for other languages
                return defaultFence(tokens, idx, options, env, slf);
              };
            });
          }

          // Return the public API
          return {
            renderMarkdown,
            processRenderedContent,
            markdownit: md
          };
        }

        /**
         * Validates Mermaid code syntax
         * @param {string} code - The Mermaid code to validate
         * @returns {Object} - Result with isValid flag and any errors
         */
        function validateMermaidCode(code) {
          try {
            // 自动修复：将含有中文、下划线、括号、等号等特殊字符的节点名用英文引号包裹
            code = code.replace(/\[([^\[\]"']+)\]/g, function(match, name) {
              // 如果已经被引号包裹则跳过
              if (/^".*"$/.test(name) || /^'.*'$/.test(name)) return match;
              // 检查是否包含中文、下划线、括号、等号等
              if (/[\u4e00-\u9fa5_()=]/.test(name)) {
                return '["' + name + '"]';
              }
              return match;
            });
            // Simple validation - check for basic syntax errors
            if (!code || typeof code !== 'string') {
              return { isValid: false, errors: ['Empty or invalid diagram code'] };
            }
            
            // 修正：处理mermaid开头的代码标记，确保有合法的图表类型
            code = code.trim();
            
            // 如果代码以```mermaid开头，移除它
            if (code.startsWith('```mermaid')) {
              code = code.substring('```mermaid'.length).trim();
            }
            
            // 如果以```结尾，移除它
            if (code.endsWith('```')) {
              code = code.substring(0, code.length - 3).trim();
            }
            
            // 处理YAML前置内容 (---title: "XXX" 等)
            if (code.includes('---title:') || code.match(/^---[\s\S]*?---/)) {
              // 尝试提取YAML前置内容和实际图表内容
              const yamlMatch = code.match(/^(---[\s\S]*?---)([\s\S]*)$/);
              if (yamlMatch) {
                // 只保留实际图表内容部分
                code = yamlMatch[2].trim();
              } else {
                // 如果无法正确分离，则移除可能引起问题的部分
                code = code.replace(/---title:.*?\n/, '');
              }
            }
            
            // 预处理：处理LaTeX数学公式，将它们替换为文本占位符，避免Mermaid解析错误
            // 这是一个核心问题修复：用简单文本替代所有的LaTeX数学公式
            let mathPlaceholders = [];
            
            // 替换所有的数学公式 $...$
            code = code.replace(/\$([^\$]+)\$/g, function(match, formula) {
              const placeholder = `[MATH_FORMULA_${mathPlaceholders.length}]`;
              mathPlaceholders.push(match);
              return placeholder;
            });
            
            // 检查for missing graph definition - 更宽松的检查
            if (!code.match(/^(graph|flowchart|sequenceDiagram|classDiagram|stateDiagram|gantt|pie|journey|requirementDiagram|gitGraph|erDiagram|mindmap|timeline|C4Context|C4Container|C4Component|C4Dynamic|C4Deployment)/i)) {
              // 如果缺少图表类型，自动添加默认的图表类型 
              code = 'graph TD;\n' + code;
            }
            
            // 检查for unmatched brackets/parentheses
            const brackets = [];
            const opens = ['(', '[', '{'];
            const closes = [')', ']', '}'];
            
            for (let i = 0; i < code.length; i++) {
              const char = code[i];
              
              if (opens.includes(char)) {
                brackets.push(char);
              } else if (closes.includes(char)) {
                const index = closes.indexOf(char);
                const matchingOpen = opens[index];
                
                if (brackets.length === 0 || brackets[brackets.length - 1] !== matchingOpen) {
                  return { 
                    isValid: true, 
                    errors: [`警告: 可能存在不匹配的括号: ${char}`], 
                    transformedCode: code,
                    mathPlaceholders: mathPlaceholders
                  };
                }
                
                brackets.pop();
              }
            }
            
            if (brackets.length > 0) {
              return { 
                isValid: true, 
                errors: [`警告: 可能存在未闭合的括号: ${brackets.join('')}`], 
                transformedCode: code,
                mathPlaceholders: mathPlaceholders
              };
            }
            
            return { 
              isValid: true, 
              errors: [], 
              transformedCode: code,
              mathPlaceholders: mathPlaceholders
            };
          } catch (error) {
            console.error("验证Mermaid代码出错:", error);
            return { isValid: true, errors: [error.message], transformedCode: code };
          }
        }
        
        /**
         * Process Mermaid diagram code and render it
         * @param {string} containerId - The ID of the container element
         * @param {string} code - The Mermaid code to render
         */
        function processMermaid(containerId, code) {
          const container = document.getElementById(containerId);
          if (!container) return;
          
          // 清理代码 - 移除可能会破坏渲染的特殊字符
          code = code.replace(/^\s+|\s+$/g, '');
          
          setTimeout(function() {
            try {
              // 首先验证代码
              const validationResult = validateMermaidCode(code);
              
              // 使用验证后的代码（可能经过了转换和修正）
              const processedCode = validationResult.transformedCode || code;
              const mathPlaceholders = validationResult.mathPlaceholders || [];
              
              if (validationResult.errors && validationResult.errors.length > 0) {
                console.warn('Mermaid validation warnings:', validationResult.errors);
                // 记录但不阻止继续渲染
              }
              
              // 将验证结果和原始数学公式存储在容器上，以便渲染后恢复
              container.setAttribute('data-math-placeholders', JSON.stringify(mathPlaceholders));
              
              // 尝试使用最合适的方法进行渲染
              if (typeof window.mermaid !== 'undefined') {
                const mermaid = window.mermaid;
                
                try {
                  // 配置Mermaid以抑制错误
                  mermaid.initialize({
                    startOnLoad: false,
                    securityLevel: 'loose',
                    suppressErrors: true, // 抑制错误显示在图表中
                    logLevel: 1, // 仅显示错误级别日志
                    theme: 'default'
                  });
                  
                  // 首先尝试最新的API (v10+)
                  if (typeof mermaid.run === 'function') {
                    container.innerHTML = processedCode;
                    mermaid.run({ 
                      nodes: [container],
                      suppressErrors: true
                    })
                    .then(() => {
                      // 渲染成功后处理SVG大小和数学公式
                      setTimeout(() => {
                        adjustMermaidDiagramSize(container);
                        // 恢复数学公式
                        restoreMathPlaceholders(container, mathPlaceholders);
                        // 处理恢复后的数学公式
                        processMermaidMath(container);
                        // 添加下载按钮
                        addDownloadButtonToMermaid(container, containerId);
                      }, 50);
                    })
                    .catch(err => {
                      console.error('Mermaid run error:', err);
                      showMermaidError(container, err.message || 'Failed to render diagram', processedCode);
                    });
                  }
                  // 然后尝试 renderAsync (v8-9)
                  else if (typeof mermaid.mermaidAPI !== 'undefined' && typeof mermaid.mermaidAPI.renderAsync === 'function') {
                    mermaid.mermaidAPI.renderAsync(containerId, processedCode)
                      .then(svgCode => {
                        container.innerHTML = svgCode;
                        // 延迟处理SVG，确保DOM完全更新
                        setTimeout(() => {
                          adjustMermaidDiagramSize(container);
                          // 恢复数学公式
                          restoreMathPlaceholders(container, mathPlaceholders);
                          processMermaidMath(container);
                          // 添加下载按钮
                          addDownloadButtonToMermaid(container, containerId);
                        }, 50);
                      })
                      .catch(err => {
                        console.error('Mermaid render error:', err);
                        showMermaidError(container, err.message || 'Failed to render diagram', processedCode);
                      });
                  }
                  // 最后回退到旧的init方法
                  else {
                    container.innerHTML = processedCode;
                    mermaid.init(undefined, container);
                    // init之后处理SVG大小和数学公式
                    setTimeout(() => {
                      adjustMermaidDiagramSize(container);
                      // 恢复数学公式
                      restoreMathPlaceholders(container, mathPlaceholders);
                      processMermaidMath(container);
                      // 添加下载按钮
                      addDownloadButtonToMermaid(container, containerId);
                    }, 100);
                  }
                } catch (err) {
                  console.error('Mermaid rendering error:', err);
                  showMermaidError(container, err.message || 'Failed to render diagram', processedCode);
                }
              } else {
                console.error('Mermaid library not available');
                showMermaidError(container, 'Mermaid library not loaded', processedCode);
              }
            } catch (err) {
              console.error('Error in processMermaid:', err);
              showMermaidError(container, err.message || 'Unknown error', code);
            }
          }, 0);
        }

        /**
         * 为Mermaid图表添加下载SVG按钮
         * @param {HTMLElement} container - 包含Mermaid图表的容器元素
         * @param {string} diagramId - 图表的唯一ID，用于SVG文件命名
         */
        function addDownloadButtonToMermaid(container, diagramId) {
          try {
            // 检查是否已经有按钮，避免重复添加
            if (container.querySelector('.mermaid-download-button')) {
              return;
            }
            
            // 查找SVG元素
            const svg = container.querySelector('svg');
            if (!svg) {
              console.log('No SVG found in container, cannot add download button');
              return;
            }
            
            // 创建按钮包装器
            const buttonWrapper = document.createElement('div');
            buttonWrapper.className = 'mermaid-buttons';
            buttonWrapper.style.position = 'absolute';
            buttonWrapper.style.top = '5px';
            buttonWrapper.style.right = '5px';
            buttonWrapper.style.zIndex = '10';
            buttonWrapper.style.display = 'flex';
            buttonWrapper.style.gap = '5px';
            
            // 创建下载按钮
            const downloadButton = document.createElement('button');
            downloadButton.className = 'mermaid-download-button';
            downloadButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg>';
            downloadButton.title = '下载SVG图表';
            downloadButton.style.background = 'rgba(255, 255, 255, 0.7)';
            downloadButton.style.border = '1px solid #ddd';
            downloadButton.style.borderRadius = '4px';
            downloadButton.style.padding = '4px';
            downloadButton.style.cursor = 'pointer';
            downloadButton.style.display = 'flex';
            downloadButton.style.alignItems = 'center';
            downloadButton.style.justifyContent = 'center';
            downloadButton.style.width = '28px';
            downloadButton.style.height = '28px';
            downloadButton.style.transition = 'all 0.2s ease';
            
            // 添加鼠标悬停效果
            downloadButton.addEventListener('mouseover', function() {
              this.style.background = 'rgba(230, 184, 77, 0.9)';
              this.style.borderColor = '#e6b84d';
              this.style.transform = 'translateY(-1px)';
              this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
            });
            
            downloadButton.addEventListener('mouseout', function() {
              this.style.background = 'rgba(255, 255, 255, 0.7)';
              this.style.borderColor = '#ddd';
              this.style.transform = 'translateY(0)';
              this.style.boxShadow = 'none';
            });
            
            // 按钮点击事件 - 下载SVG
            downloadButton.addEventListener('click', function() {
              // 克隆SVG以便修改
              const clonedSvg = svg.cloneNode(true);
              
              // 移除外部容器的大小限制，让SVG以原始大小导出
              clonedSvg.style.maxWidth = 'none';
              clonedSvg.style.width = clonedSvg.getAttribute('width') || '100%';
              
              // 确保图表有viewBox属性
              if (!clonedSvg.getAttribute('viewBox') && clonedSvg.getAttribute('width') && clonedSvg.getAttribute('height')) {
                const width = parseFloat(clonedSvg.getAttribute('width'));
                const height = parseFloat(clonedSvg.getAttribute('height'));
                if (!isNaN(width) && !isNaN(height)) {
                  clonedSvg.setAttribute('viewBox', `0 0 ${width} ${height}`);
                }
              }
              
              // 转换SVG为可下载格式
              const serializer = new XMLSerializer();
              let svgString = serializer.serializeToString(clonedSvg);
              
              // 添加XML声明和SVG命名空间
              if (!svgString.includes('<?xml')) {
                svgString = '<?xml version="1.0" encoding="UTF-8" standalone="no"?>' + svgString;
              }
              if (!svgString.includes('xmlns="http://www.w3.org/2000/svg"')) {
                svgString = svgString.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"');
              }
              
              // 创建Blob对象
              const svgBlob = new Blob([svgString], { type: 'image/svg+xml;charset=utf-8' });
              const svgUrl = URL.createObjectURL(svgBlob);
              
              // 创建下载链接
              const downloadLink = document.createElement('a');
              downloadLink.href = svgUrl;
              downloadLink.download = `mermaid-diagram-${diagramId.replace(/mermaid-diagram-/g, '')}.svg`;
              document.body.appendChild(downloadLink);
              downloadLink.click();
              document.body.removeChild(downloadLink);
              
              // 释放URL对象
              setTimeout(() => {
                URL.revokeObjectURL(svgUrl);
              }, 100);
            });
            
            // 将按钮添加到按钮容器
            buttonWrapper.appendChild(downloadButton);
            
            // 为容器添加相对定位，以便正确放置按钮
            container.style.position = 'relative';
            container.style.overflow = 'visible';
            
            // 将按钮容器添加到图表容器
            container.appendChild(buttonWrapper);
            
          } catch (error) {
            console.error('Error adding download button to Mermaid diagram:', error);
          }
        }

        /**
         * 调整 Mermaid 图表大小以适应容器
         * @param {HTMLElement} container - 包含 Mermaid 图表的容器元素
         */
        function adjustMermaidDiagramSize(container) {
          if (!container) return;
          
          try {
            // 查找容器中的SVG元素
            const svg = container.querySelector('svg');
            if (svg) {
              // 设置SVG为相对大小，最大宽度为上层容器的70%
              svg.style.maxWidth = '70%';
              svg.style.width = '100%';
              
              // 使用style设置高度而不是属性
              svg.style.height = 'auto';
              svg.style.margin = '0 auto'; // 水平居中
              svg.style.display = 'block'; // 确保居中生效
              
              // 确保SVG有viewBox以保持适当的缩放
              if (!svg.getAttribute('viewBox') && svg.getAttribute('width') && svg.getAttribute('height')) {
                const width = parseFloat(svg.getAttribute('width'));
                const height = parseFloat(svg.getAttribute('height'));
                if (!isNaN(width) && !isNaN(height)) {
                  svg.setAttribute('viewBox', `0 0 ${width} ${height}`);
                }
              }
              
              // 移除SVG的height属性，但保留style.height
              if (svg.hasAttribute('height')) {
                svg.removeAttribute('height');
              }
            }
          } catch (err) {
            console.error('Error adjusting Mermaid diagram size:', err);
          }
        }
        
        /**
         * Display error message in Mermaid container
         * @param {HTMLElement} container - The container element
         * @param {string} message - The error message to display
         * @param {string} code - The original Mermaid code
         */
        function showMermaidError(container, message, code) {
          // 创建错误容器
          const errorContainer = document.createElement('div');
          errorContainer.className = 'mermaid-error-container';
          errorContainer.textContent = message || 'Failed to render diagram. Please check your syntax.';
          
          // 在错误信息下显示代码片段
          const codePreview = document.createElement('pre');
          codePreview.className = 'mermaid-error-code';
          codePreview.style.marginTop = '10px';
          codePreview.style.padding = '8px';
          codePreview.style.backgroundColor = 'rgba(0,0,0,0.05)';
          codePreview.style.borderRadius = '4px';
          codePreview.style.fontFamily = 'monospace';
          codePreview.style.fontSize = '12px';
          codePreview.style.overflow = 'auto';
          codePreview.style.maxHeight = '100px';
          codePreview.textContent = code || '(无代码)';
          
          // 创建重试按钮
          const retryButton = document.createElement('button');
          retryButton.textContent = '尝试默认主题渲染';
          retryButton.style.marginTop = '10px';
          retryButton.style.padding = '5px 10px';
          retryButton.style.backgroundColor = '#e6b84d';
          retryButton.style.color = 'white';
          retryButton.style.border = 'none';
          retryButton.style.borderRadius = '4px';
          retryButton.style.cursor = 'pointer';
          
          retryButton.onclick = function() {
            try {
              // 尝试使用最小配置
              const mermaidCode = container.getAttribute('data-diagram-code') || code || '';
              container.innerHTML = mermaidCode;
              
              if (typeof window.mermaid !== 'undefined') {
                const mermaid = window.mermaid;
                // 强制使用默认主题和最简配置
                mermaid.initialize({
                  startOnLoad: false,
                  theme: 'default',
                  logLevel: 5, // 详细日志以便调试
                  securityLevel: 'loose'
                });
                
                // 尝试渲染
                if (typeof mermaid.run === 'function') {
                  mermaid.run({ nodes: [container] })
                    .then(() => adjustMermaidDiagramSize(container))
                    .catch(e => console.error('最终mermaid渲染尝试失败:', e));
                } else {
                  mermaid.init(undefined, container);
                  setTimeout(() => adjustMermaidDiagramSize(container), 100);
                }
              }
            } catch (e) {
              console.error('最终mermaid渲染尝试失败:', e);
              container.innerHTML = '';
              container.appendChild(errorContainer);
              container.appendChild(codePreview);
              container.appendChild(retryButton);
            }
          };
          
          // 清空并显示错误与重试按钮
          container.innerHTML = '';
          container.appendChild(errorContainer);
          container.appendChild(codePreview);
          container.appendChild(retryButton);
        }

        /**
         * Process math formulas inside a Mermaid diagram
         * @param {HTMLElement} container - The container with the rendered Mermaid diagram
         */
        function processMermaidMath(container) {
          if (!container) return;
          
          try {
            // 检查SVG元素是否存在
            const svg = container.querySelector('svg');
            if (!svg) {
              console.log('No SVG element found in Mermaid diagram container');
              return;
            }
            
            // Find all text elements in the SVG that might contain math formulas
            const textElements = svg.querySelectorAll('text, .noteText tspan, .noteText');
            
            if (!textElements || textElements.length === 0) {
              console.log('No text elements found in Mermaid diagram SVG');
              return;
            }
            
            // Process Note boxes specifically - they're better targets for replacement
            const noteRects = svg.querySelectorAll('.note');
            if (noteRects && noteRects.length > 0) {
              noteRects.forEach(noteRect => {
                try {
                  // Get all the text elements in this note
                  const noteTexts = noteRect.parentNode.querySelectorAll('.noteText tspan');
                  
                  if (!noteTexts || noteTexts.length === 0) return;
                  
                  // Combine all text content 
                  let fullText = '';
                  noteTexts.forEach(tspan => {
                    fullText += tspan.textContent + ' ';
                  });
                  
                  // Check if there are math formulas
                  if (fullText.includes('$') && (fullText.includes('\\') || fullText.includes('{'))) {
                    // Create a completely new foreign object to replace the note content
                    const svgNS = "http://www.w3.org/2000/svg";
                    const foreignObject = document.createElementNS(svgNS, "foreignObject");
                    
                    // Get the note rectangle dimensions
                    const rectBBox = noteRect.getBBox();
                    foreignObject.setAttribute("x", rectBBox.x + 5); // Add padding
                    foreignObject.setAttribute("y", rectBBox.y + 5); 
                    foreignObject.setAttribute("width", rectBBox.width - 10);
                    foreignObject.setAttribute("height", rectBBox.height - 10);
                    
                    // Create HTML content to replace the note text
                    const div = document.createElement("div");
                    div.style.width = "100%";
                    div.style.height = "100%";
                    div.style.display = "flex";
                    div.style.alignItems = "center";
                    div.style.justifyContent = "center";
                    div.style.padding = "5px";
                    div.style.boxSizing = "border-box";
                    
                    // Replace all math formulas with their rendered versions
                    let processedText = fullText;
                    const mathRegex = /\$(.*?)\$/g;
                    let match;
                    
                    // Collect all formula matches
                    const formulas = [];
                    while ((match = mathRegex.exec(fullText)) !== null) {
                      formulas.push({
                        full: match[0],
                        formula: match[1],
                        index: match.index
                      });
                    }
                    
                    // If we found formulas, process them
                    if (formulas.length > 0) {
                      let htmlContent = '';
                      let lastIndex = 0;
                      
                      formulas.forEach(item => {
                        // Add text before the formula
                        htmlContent += processedText.substring(lastIndex, item.index);
                        
                        try {
                          // Process formula to handle Chinese characters
                          const processedFormula = processMathFormula(item.formula);
                          
                          // Render the formula with KaTeX
                          const renderedFormula = window.katex.renderToString(processedFormula, {
                            displayMode: false,
                            throwOnError: false,
                            output: 'html'
                          });
                          
                          htmlContent += renderedFormula;
                        } catch (err) {
                          console.warn('Error rendering formula:', err);
                          htmlContent += item.full; // Keep original on error
                        }
                        
                        lastIndex = item.index + item.full.length;
                      });
                      
                      // Add remaining text after the last formula
                      htmlContent += processedText.substring(lastIndex);
                      
                      // Set the HTML content
                      div.innerHTML = htmlContent;
                      
                      // Add the foreign object to the SVG
                      foreignObject.appendChild(div);
                      
                      // Find the note group and add the foreign object
                      const noteGroup = noteRect.parentNode;
                      if (noteGroup) {
                        noteGroup.appendChild(foreignObject);
                        
                        // Hide the original text elements
                        noteTexts.forEach(tspan => {
                          tspan.textContent = '';
                        });
                      }
                    }
                  }
                } catch (err) {
                  console.error('Error processing note:', err);
                }
              });
            }

            // Process individual text elements (non-note elements)
            textElements.forEach(textEl => {
              const text = textEl.textContent;
              
              // Skip processing if the text doesn't contain potential math formula markers
              if (!text || !(text.includes('$') && (text.includes('\\') || text.includes('{'))) ) {
                return;
              }
              
              // Check for inline math formulas: $formula$
              const inlineMatch = text.match(/\$(.*?)\$/g);
              if (inlineMatch) {
                let processedText = text;
                
                inlineMatch.forEach(formula => {
                  try {
                    // Extract the formula without the delimiters
                    const formulaContent = formula.slice(1, -1);
                    
                    // Process formula to handle Chinese characters
                    const processedFormula = processMathFormula(formulaContent);
                    
                    // Render the formula
                    const renderedFormula = window.katex.renderToString(processedFormula, {
                      displayMode: false,
                      throwOnError: false,
                      output: 'html'
                    });
                    
                    // Create a foreign object to embed HTML in SVG
                    const svgNS = "http://www.w3.org/2000/svg";
                    const foreignObject = document.createElementNS(svgNS, "foreignObject");
                    
                    // Position it where the text element is
                    const bbox = textEl.getBBox();
                    foreignObject.setAttribute("x", bbox.x);
                    foreignObject.setAttribute("y", bbox.y);
                    foreignObject.setAttribute("width", "100%");
                    foreignObject.setAttribute("height", "100%");
                    
                    // Set the HTML content with the rendered formula
                    const div = document.createElement("div");
                    div.style.display = "inline-block";
                    div.style.fontSize = window.getComputedStyle(textEl).fontSize;
                    div.style.color = window.getComputedStyle(textEl).color;
                    div.innerHTML = renderedFormula;
                    
                    foreignObject.appendChild(div);
                    
                    // Replace formula reference in text with a placeholder
                    const placeholder = `[MATH_${Math.random().toString(36).substring(2, 10)}]`;
                    processedText = processedText.replace(formula, placeholder);
                    
                    // If this is the only formula, replace the text node completely
                    if (text === formula) {
                      const parent = textEl.parentNode;
                      if (parent) {
                        parent.appendChild(foreignObject);
                        textEl.textContent = ""; // Hide original text
                      }
                    }
                  } catch (err) {
                    console.warn('Error rendering math in Mermaid:', err);
                  }
                });
                
                // If we had multiple formulas or text with formulas, update the text content
                if (text !== inlineMatch[0] && inlineMatch.length >= 1) {
                  textEl.textContent = processedText.replace(/\[MATH_[a-z0-9]+\]/g, ''); // Remove placeholders
                }
              }
            });
            
            // Make the diagram responsive
            if (svg) {
              svg.setAttribute('width', '100%');
              svg.style.maxWidth = '70%'; // 更新为70%宽度
              svg.style.height = 'auto';
              svg.style.margin = '0 auto';
              svg.style.display = 'block';
              
              // Add missing viewBox if needed
              if (!svg.getAttribute('viewBox') && svg.getAttribute('width') && svg.getAttribute('height')) {
                const width = parseFloat(svg.getAttribute('width'));
                const height = parseFloat(svg.getAttribute('height'));
                if (!isNaN(width) && !isNaN(height)) {
                  svg.setAttribute('viewBox', `0 0 ${width} ${height}`);
                }
              }
            }
            
          } catch (err) {
            console.error('Error processing math in Mermaid diagram:', err);
          }
        }

        /**
         * Helper function to process Chinese characters in KaTeX formulas
         * @param {string} formula - The formula string
         * @returns {string} - Processed formula with Chinese characters in \text{}
         */
        function processMathFormula(formula) {
          if (!formula) return formula;
          
          // Handle Chinese characters in math formulas by wrapping them in \text{}
          // This regex matches sequences of Chinese characters
          return formula.replace(/([\u4e00-\u9fa5，：；？！（）]+)/g, '\\text{$1}');
        }

        /**
         * 恢复被替换的数学公式占位符
         * @param {HTMLElement} container - 包含Mermaid图表的容器元素
         * @param {Array} mathPlaceholders - 数学公式占位符数组
         */
        function restoreMathPlaceholders(container, mathPlaceholders) {
          if (!container || !mathPlaceholders || mathPlaceholders.length === 0) return;
          
          try {
            // 查找所有文本节点
            const textNodes = [];
            const walker = document.createTreeWalker(container, NodeFilter.SHOW_TEXT, null, false);
            let node;
            while (node = walker.nextNode()) {
              textNodes.push(node);
            }
            
            // 处理每个文本节点
            textNodes.forEach(textNode => {
              let content = textNode.nodeValue;
              
              // 检查是否包含占位符
              const placeholderRegex = /\[MATH_FORMULA_(\d+)\]/g;
              let match;
              let replaced = false;
              
              while ((match = placeholderRegex.exec(content)) !== null) {
                replaced = true;
                const index = parseInt(match[1]);
                if (index >= 0 && index < mathPlaceholders.length) {
                  const placeholder = match[0];
                  const formula = mathPlaceholders[index];
                  
                  // 创建一个span来包含渲染后的数学公式
                  const span = document.createElement('span');
                  span.className = 'mermaid-math-formula';
                  span.setAttribute('data-latex', formula);
                  
                  try {
                    // 尝试使用KaTeX渲染公式
                    if (window.katex) {
                      // 提取不含$的公式内容
                      const formulaContent = formula.replace(/^\$|\$$/g, '');
                      katex.render(formulaContent, span, {
                        throwOnError: false,
                        displayMode: false
                      });
                    } else {
                      // 如果没有KaTeX，则显示原始公式
                      span.textContent = formula;
                    }
                  } catch (err) {
                    console.warn('Error rendering math in Mermaid:', err);
                    span.textContent = formula;
                  }
                  
                  // 分割文本节点并插入渲染后的数学公式
                  const beforeText = content.substring(0, content.indexOf(placeholder));
                  const afterText = content.substring(content.indexOf(placeholder) + placeholder.length);
                  
                  // 创建前后的文本节点
                  const beforeNode = document.createTextNode(beforeText);
                  const afterNode = document.createTextNode(afterText);
                  
                  // 替换原始文本节点
                  const parent = textNode.parentNode;
                  if (parent) {
                    parent.insertBefore(beforeNode, textNode);
                    parent.insertBefore(span, textNode);
                    parent.insertBefore(afterNode, textNode);
                    parent.removeChild(textNode);
                    
                    // 更新下一个要处理的文本节点
                    textNode = afterNode;
                    content = afterText;
                    placeholderRegex.lastIndex = 0; // 重置正则表达式搜索位置
                  }
                }
              }
              
              // 如果没有替换发生，保持原始节点不变
              if (!replaced) {
                return;
              }
            });
          } catch (err) {
            console.error('Error restoring math formulas in Mermaid diagram:', err);
          }
        }

        function toggleEnableThinking() {
            const searchMethod = document.getElementById('search_method');
            const enableThinkingDiv = document.getElementById('enable_thinking');
            if (!searchMethod || !enableThinkingDiv) return;
            const showMethods = ['聊天', '本地', '联网', '本地+联网', '润色'];
            if (showMethods.includes(searchMethod.value)) {
                enableThinkingDiv.style.cssText = "flex: 0 0 190px; display: flex !important; align-items: center; border: 1px solid #E6B84D; border-radius: 5px; padding: 5px; min-width: 190px; max-width: 190px;";
                enableThinkingDiv.removeAttribute('hidden');
            } else {
                enableThinkingDiv.style.cssText = "flex: 0 0 190px; display: none !important; align-items: center; border: 1px solid #E6B84D; border-radius: 5px; padding: 5px; min-width: 190px; max-width: 190px;";
                enableThinkingDiv.setAttribute('hidden', '');
            }
        }
        // 页面加载和search_method变更时都要调用
        document.addEventListener('DOMContentLoaded', function() {
            const searchMethod = document.getElementById('search_method');
            if (searchMethod) {
                toggleEnableThinking();
                searchMethod.addEventListener('change', function() {
                    toggleEnableThinking();
                });
            }
        });
    </script>
</head>
<body>
    <div id="aiChatSection">
        <div class="drag-handle" onmousedown="startDrag(event)"></div>  <!-- 拖动手柄 -->
        <h2>AI 对话助手</h2>

        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <button id="toggle-button" class="toggle-button toggle-button-collapsed" onclick="toggleFileSelection()">
                <span class="toggle-icon"></span>
                <span class="toggle-text">展开选项卡</span>
            </button>
            <button id="selectAllButton" onclick="toggleSelectAll()">全选/取消全选</button>
        </div>
        <div id="file-selection-container" style="max-height: 400px; overflow-y: auto; display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
            <div class="file-item" onclick="toggleCheckbox(this)">
                <input type="checkbox" id="file1" value="file1">
                <label for="file1" class="file-name">文件1</label>
            </div>
            <div class="file-item" onclick="toggleCheckbox(this)">
                <input type="checkbox" id="file2" value="file2">
                <label for="file2" class="file-name">文件2</label>
            </div>
            <!-- 其他文件项 -->
        </div>

        <!-- 这些样式已经在上面统一定义了，这里删除重复定义 -->

        <!-- 新增选项卡 -->
        <div class="options-container">
            <div>
                <label for="language">输出语言</label>
                <select id="language">
                    <option value="Chinese">Chinese</option>
                    <option value="English">English</option>
                </select>
            </div>

            <div>
                <label for="summary_length">输出字数</label>
                <input type="number" id="summary_length" min="10" value="100" step="100" style="width: 80px;">
            </div>

            <div>
                <label for="query_source">查询语言</label>
                <select id="query_source">
                    <option value="用户输入">用户输入</option>
                    <option value="英文翻译">英文翻译</option>
                </select>
            </div>

            <div>
                <label for="net_method">搜索引擎</label>
                <select id="net_method">
                    <option value="duckduckgo">duckduckgo</option>
                    <option value="百度">百度</option>
                </select>
            </div>

            <div style="flex: 0 0 280px; min-width: 280px; max-width: 290px;">
                <label for="search_method">生成方法</label>
                <select id="search_method" style="min-width: 100px;">
                    <option value="聊天">聊天</option>
                    <option value="本地">本地</option>
                    <option value="联网">深度网络搜索</option>
                    <option value="润色">润色</option>
                    <option value="本地+联网">本地+联网</option>
                    <option value="论文搜索">Arxiv论文搜索</option>
                    <option value="论文搜索1">ProQuest论文搜索</option>
                </select>
            </div>

            <div id="paper_count_container" style="flex: 0 0 320px; display: none !important; min-width: 320px; max-width: 320px; justify-content: space-between;">
                <label for="paper_count" style="white-space: nowrap; flex: 1;">Arxiv论文检索数量</label>
                <input type="number" id="paper_count" min="1" max="15" value="3" style="width: 40px;">
            </div>

            <div id="paper_count_container_proquest" style="flex: 0 0 320px; display: none !important; min-width: 320px; max-width: 320px; justify-content: space-between;">
                <label for="paper_count_proquest" style="white-space: nowrap; flex: 1;">ProQuest论文检索数量</label>
                <input type="number" id="paper_count_proquest" min="1" max="20" value="3" style="width: 40px;">
            </div>
            <div id="web_deep_search" style="flex: 0 0 190px; display: none !important; min-width: 190px; max-width: 190px;">
                <input type="checkbox" id="deep_search_enabled">
                <label for="deep_search_enabled" style="margin-left: 5px;">启用深度搜索</label>
            </div>
            <div id="enable_thinking" style="flex: 0 0 190px; display: none; min-width: 190px; max-width: 190px;">
                <input type="checkbox" id="enable_thinking_checkbox">
                <label for="enable_thinking_checkbox" style="margin-left: 5px;">启用思考</label>
            </div>
            <button class="submit-button" onclick="submitOptions()" style="flex: 0 0 120px; margin-left: auto;">保存设置</button>
        </div>

        <div class="chat-info-container">
            <div class="chat-info-trigger">
                <span style="color: #4A90E2;">ℹ️</span> 查看使用限制
            </div>
            <div class="chat-limits">
                <div class="limit-item">
                    <div><span class="limit-icon">💬</span>每日对话次数上限: 50次</div>
                    <div><span class="limit-icon">🌐</span>联网模式次数: 60次</div>
                    <div class="remaining-count" id="chatCountDisplay">
                        <span class="count-icon">🎯</span>
                        剩余对话次数: <span id="chatCount">--</span>
                    </div>
                </div>
            </div>
        </div>

        <div id="chatHistory"></div>
        
        <!-- 修改后的输入区域结构 -->
        <div class="chat-input">
            <div class="chat-input-wrapper">
                <textarea id="userInput" placeholder="输入你的问题..."></textarea>
                <div class="button-group">
                    <button id="sendButton" onclick="sendMessage()">发送</button>
                    <button id="newChatButton" onclick="startNewChat()">新对话</button>
                    <button id="downloadButton" onclick="downloadChat()">💾 保存对话</button>
                </div>
            </div>
        </div>

    </div>

    <script>
        const urlParams = new URLSearchParams(window.location.search);
        const user = urlParams.get('user');
        const key = urlParams.get('key');
        const token = urlParams.get('token');  // 获取 token 参数
        // 存储对话历史
        const messageHistoryfile = [];
        

       
        async function sendMessage() {
            const userInput = document.getElementById('userInput').value;
            const sendButton = document.getElementById('sendButton');
            const newChatButton = document.getElementById('newChatButton');
            if (!userInput) return;

            appendMessage('You', userInput);
            messageHistoryfile.push({role: 'You', content: userInput}); // 添加用户消息到历史记录
            document.getElementById('userInput').value = '';
            sendButton.disabled = true;
            newChatButton.disabled = true;
            sendButton.textContent = '等待中...';
            // 获取最新的10000个字符的对话历史
            let recentHistory = getRecentHistory(20000);


            try {
                const response = await Promise.race([
                    fetch('/api/filechat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: userInput,
                            user: user,
                            key: key,
                            history: recentHistory // 发送对话历史                                
                        })
                    }),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('请求超时')), 2500000)) // 设置超时为5秒
                ]);

                const data = await response.json();

                if (data.success) {
                    appendMessage('AI', data.response);
                    messageHistoryfile.push({role: 'AI', content: data.response}); // 添加AI回复到历史记录
                    updateRemainingChats(data.remainingChats);
                } else {
                    appendMessage('System', 'Error: ' + data.message);
                }
            } catch (error) {
                console.error('Error:', error);
                appendMessage('System', 'An error occurred while sending the message: ' + error.message);
            } finally {
                sendButton.disabled = false;
                newChatButton.disabled = false;
                sendButton.textContent = '发送';
            }
        }

        
        // 获取最新的对话历史，确保不超过指定的汉字和英文单词总数
        function getRecentHistory(maxWords) {
            let totalWords = 0;
            let recentMessages = [];

            // 从最新的消息开始向前遍历
            for (let i = messageHistoryfile.length - 1; i >= 0; i--) {
                const message = messageHistoryfile[i];
                        // 只处理AI的回复消息
                        // 只处理AI的回复消息
            if (message.role === 'AI') {
                const messageText = message.content + '\n';

                    // 使用正则表达式分别匹配汉字和英文单词
                const chineseWords = messageText.match(/[\u4e00-\u9fa5]/g) || [];
                const englishWords = messageText.match(/\b\w+\b/g) || [];

                const currentWordCount = chineseWords.length + englishWords.length;

                // 如果添加当前消息后总单词数仍在限制内
                if (totalWords + currentWordCount <= maxWords) {
                    recentMessages.unshift(messageText);
                    totalWords += currentWordCount;
                } else {
                    // 如果当前消息会导致超出限制，检查是否可以添加部分内容
                    const remainingWords = maxWords - totalWords;
                    if (remainingWords > 0) {
                        // 截取部分内容
                        let partialMessage = '';
                        let partialChinese = 0;
                        let partialEnglish = 0;

                        for (let char of messageText) {
                            if (/[\u4e00-\u9fa5]/.test(char)) {
                                if (partialChinese < remainingWords) {
                                    partialMessage += char;
                                    partialChinese++;
                                } else {
                                    break;
                                }
                            } else if (/\b\w+\b/.test(char)) {
                                if (partialEnglish < remainingWords) {
                                    partialMessage += char;
                                    partialEnglish++;
                                } else {
                                    break;
                                }
                            } else {
                                partialMessage += char;
                            }
                        }

                        recentMessages.unshift(partialMessage);
                    }
                    break;
                }
            }
        }
            return recentMessages.join('');
        }

        
        // 添加下载对话内容的函数
        function downloadChat() {
            // 获取当前时间作为文件名
            const now = new Date();
            const fileName = `chat_history_${now.getFullYear()}${(now.getMonth()+1).toString().padStart(2,'0')}${now.getDate().toString().padStart(2,'0')}_${now.getHours().toString().padStart(2,'0')}${now.getMinutes().toString().padStart(2,'0')}.txt`;
            
            // 创建对话内容
            let chatContent = "对话历史记录\n\n";
            chatContent += "时间：" + now.toLocaleString() + "\n\n";
            
            // 将消息历史转换为文本
            messageHistoryfile.forEach(msg => {
                chatContent += `${msg.role}: ${msg.content}\n\n`;
            });
            
            // 创建Blob对象
            const blob = new Blob([chatContent], { type: 'text/plain;charset=utf-8' });
            
            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = fileName;
            
            // 添加到文档并触发下载
            document.body.appendChild(a);
            a.click();
            
            // 清理
            window.URL.revokeObjectURL(url);
            
            // 显示成功消息
            appendMessage('System', '✅ 对话内容已成功保存');
        }



        // 添加新对话函数
        function startNewChat() {
            // 清空聊天历史显示
            const chatHistory = document.getElementById('chatHistory');
            chatHistory.innerHTML = '';
            
            // 清空消息历史数组
            messageHistoryfile.length = 0;
            
            // 清空输入框
            document.getElementById('userInput').value = '';
            
            // 添加系统消息提示新对话开始
            appendMessage('System', '🌟 开始新的对话 🌟');
        }



    // 确保这个函数被定义
    function updateRemainingChats(remainingChats) {
        const chatCountDisplay = document.getElementById('chatCountDisplay');
        const chatCount = document.getElementById('chatCount');
        if (chatCountDisplay && chatCount) {
            chatCountDisplay.style.display = 'inline-block';
            chatCount.textContent = remainingChats;
            console.log('Updated remaining chats:', remainingChats);
        } else {
            console.warn('Chat count elements not found');
        }
    }

    // 初始化聊天限制信息显示
    function initChatLimits() {
        const trigger = document.querySelector('.chat-info-trigger');
        const limits = document.querySelector('.chat-limits');

        if (trigger && limits) {
            trigger.addEventListener('click', function() {
                limits.classList.toggle('show');
            });
        }

        // 初始化显示剩余次数（如果有的话）
        const chatCountDisplay = document.getElementById('chatCountDisplay');
        if (chatCountDisplay) {
            chatCountDisplay.style.display = 'inline-block';
        }
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        initChatLimits();

        // 尝试从后端获取当前剩余次数
        fetchRemainingChats();
    });

    // 获取剩余对话次数
    async function fetchRemainingChats() {
        try {
            const response = await fetch(`/api/remaining-chats?t=${Date.now()}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                cache: 'no-cache'
            });

            if (response.ok) {
                const data = await response.json();
                if (data.remainingChats !== undefined) {
                    updateRemainingChats(data.remainingChats);
                }
            }
        } catch (error) {
            console.log('Could not fetch remaining chats:', error);
            // 如果无法获取，显示默认值
            updateRemainingChats('--');
        }
    }

        // 初始化MarkdownRenderer
        let markdownRenderer;
        
        // 等待文档加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化Markdown渲染器
            markdownRenderer = initMarkdownRenderer({
                markdownit: window.markdownit,
                hljs: window.hljs,
                katex: window.katex,
                mermaid: window.mermaid
            });
            
            if (!markdownRenderer) {
                console.error("无法初始化Markdown渲染器，缺少必要的依赖项");
                return;
            }
            
            console.log("Markdown渲染器初始化成功");
        });

        // 添加消息到聊天历史记录
        function appendMessage(sender, message) {
            const chatHistory = document.getElementById('chatHistory');
            const messageElement = document.createElement('div');
            messageElement.className = sender === 'You' ? 'you-message' : (sender === 'System' ? 'system-message' : 'ai-message');
            
            // 添加淡入动画效果
            messageElement.style.opacity = '0';
            messageElement.style.transform = 'translateY(10px)';
            messageElement.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            
            // 创建发送者元素
            const senderElement = document.createElement('div');
            senderElement.className = 'message-sender';
            senderElement.textContent = sender === 'You' ? '👤 用户' : sender === 'AI' ? '🤖 AI助手' : '⚙️ 系统';
            
            // 为发送者图标添加动画类
            const emojiSpan = document.createElement('span');
            emojiSpan.className = 'sender-emoji';
            emojiSpan.textContent = sender === 'You' ? '👤 ' : sender === 'AI' ? '🤖 ' : '⚙️ ';
            
            // 设置文本内容（不含emoji）
            const nameSpan = document.createElement('span');
            nameSpan.textContent = sender === 'You' ? '用户' : sender === 'AI' ? 'AI助手' : '系统';
            
            // 清空并重新组装发送者元素
            senderElement.textContent = '';
            senderElement.appendChild(emojiSpan);
            senderElement.appendChild(nameSpan);
            
            // 创建内容元素
            const contentElement = document.createElement('div');
            contentElement.className = 'message-content rendered-markdown';
            
            // 如果是系统消息，直接添加内容
            if (sender === 'System') {
                // 创建系统消息发送者元素
                const systemSenderElement = document.createElement('div');
                systemSenderElement.className = 'message-sender';
                
                // 添加系统图标和名称
                const systemEmojiSpan = document.createElement('span');
                systemEmojiSpan.className = 'sender-emoji';
                systemEmojiSpan.textContent = '⚙️ ';
                
                const systemNameSpan = document.createElement('span');
                systemNameSpan.textContent = '系统';
                
                systemSenderElement.appendChild(systemEmojiSpan);
                systemSenderElement.appendChild(systemNameSpan);
                
                // 创建系统消息内容元素
                const systemContentElement = document.createElement('div');
                systemContentElement.className = 'message-content';
                systemContentElement.textContent = message;
                
                // 构建系统消息元素
                messageElement.appendChild(systemSenderElement);
                messageElement.appendChild(systemContentElement);
                
                chatHistory.appendChild(messageElement);
                
                // 执行淡入动画
                setTimeout(() => {
                    messageElement.style.opacity = '1';
                    messageElement.style.transform = 'translateY(0)';
                }, 10);
                
                chatHistory.scrollTop = chatHistory.scrollHeight;
                return messageElement;
            }
            
            // 设置当前处理的消息来源
            window.currentProcessingSource = sender.toLowerCase();
            
            try {
                // 使用封装的Markdown渲染器渲染内容
                if (markdownRenderer) {
                    // 渲染Markdown
                    let renderedContent = markdownRenderer.renderMarkdown(message);
                    contentElement.innerHTML = renderedContent;
                    
                    // 构建消息元素
                    messageElement.appendChild(senderElement);
                    messageElement.appendChild(contentElement);
                    
                    // 添加到聊天历史
                    chatHistory.appendChild(messageElement);
                    
                    // 执行淡入动画
                    setTimeout(() => {
                        messageElement.style.opacity = '1';
                        messageElement.style.transform = 'translateY(0)';
                    }, 10);
                    
                    // 处理渲染后的内容（代码高亮等）
                    markdownRenderer.processRenderedContent(contentElement);
                    
                    // 自动滚动到底部
                    chatHistory.scrollTop = chatHistory.scrollHeight;
                    
                } else {
                    // 如果渲染器未初始化，使用纯文本
                    console.warn("Markdown渲染器未初始化，使用纯文本");
                    contentElement.textContent = message;
                    messageElement.appendChild(senderElement);
                    messageElement.appendChild(contentElement);
                    chatHistory.appendChild(messageElement);
                    
                    // 执行淡入动画
                    setTimeout(() => {
                        messageElement.style.opacity = '1';
                        messageElement.style.transform = 'translateY(0)';
                    }, 10);
                    
                    // 自动滚动到底部
                    chatHistory.scrollTop = chatHistory.scrollHeight;
                }
            } catch (error) {
                console.error('处理消息内容时出错:', error);
                // 出错时使用原始文本
                contentElement.textContent = message;
                messageElement.appendChild(senderElement);
                messageElement.appendChild(contentElement);
                chatHistory.appendChild(messageElement);
                
                // 执行淡入动画
                setTimeout(() => {
                    messageElement.style.opacity = '1';
                    messageElement.style.transform = 'translateY(0)';
                }, 10);
                
                // 自动滚动到底部
                chatHistory.scrollTop = chatHistory.scrollHeight;
            }
            
            return messageElement;
        }

        function initializeMessageElement(element) {
            // 使用封装的Markdown渲染器处理渲染后的内容
            if (markdownRenderer) {
                markdownRenderer.processRenderedContent(element);
                
                // 确保添加rendered-markdown类以应用样式
                if (!element.classList.contains('rendered-markdown') && !element.parentElement.classList.contains('rendered-markdown')) {
                    element.classList.add('rendered-markdown');
                }
            } else {
                console.warn('Markdown渲染器未初始化，使用原始方法处理消息内容');
                
                // 渲染数学公式
                try {
                    if (typeof renderMathInElement === 'function') {
                        renderMathInElement(element, {
                            delimiters: [
                                {left: "$$", right: "$$", display: true},
                                {left: "$", right: "$", display: false}
                            ],
                            throwOnError: false
                        });
                    } else {
                        console.warn('renderMathInElement函数不可用，跳过公式渲染');
                    }
                } catch (mathError) {
                    console.error('数学公式渲染错误:', mathError);
                }

                // 应用代码高亮
                try {
                    element.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightElement(block);
                    });
                    
                    // 为代码块添加复制按钮
                    element.querySelectorAll('pre').forEach(pre => {
                        if (!pre.querySelector('.copy-code-button')) {
                            const copyButton = document.createElement('button');
                            copyButton.className = 'copy-code-button';
                            copyButton.textContent = '复制';
                            
                            copyButton.addEventListener('click', function() {
                                const code = pre.querySelector('code');
                                if (code) {
                                    const text = code.textContent;
                                    navigator.clipboard.writeText(text).then(
                                        function() {
                                            copyButton.textContent = '已复制!';
                                            setTimeout(function() {
                                                copyButton.textContent = '复制';
                                            }, 2000);
                                        },
                                        function(err) {
                                            console.error('无法复制文本: ', err);
                                            copyButton.textContent = '复制失败';
                                            setTimeout(function() {
                                                copyButton.textContent = '复制';
                                            }, 2000);
                                        }
                                    );
                                }
                            });
                            
                            pre.style.position = 'relative';
                            pre.appendChild(copyButton);
                        }
                    });
                } catch (highlightError) {
                    console.error('代码高亮错误:', highlightError);
                }
            }
        }

        function submitOptions(showAlert = true) {
            const language = document.getElementById('language').value;
            const summary_length = document.getElementById('summary_length').value;
            const query_source = document.getElementById('query_source').value;
            const net_method = document.getElementById('net_method').value;
            const search_method = document.getElementById('search_method').value;
            const deep_search_enabled = document.getElementById('deep_search_enabled').checked ?  true : false;
            const enable_thinking = document.getElementById('enable_thinking_checkbox').checked ? true : false;
            // Update the way we get max_results based on which search type is selected
            let max_results;
            if (search_method === '论文搜索') {
                // Arxiv search
                max_results = document.getElementById('paper_count').value;
            } else if (search_method === '论文搜索1') {
                // ProQuest search
                max_results = document.getElementById('paper_count_proquest').value;
            } else {
                // Default to Arxiv value if neither is selected
                max_results = document.getElementById('paper_count').value;
            }
            
            // 从 sessionStorage 获取 selected_doc_ids
            const selected_doc_ids = sessionStorage.getItem('selected_doc_ids') || '';

            const data = {
                user: user,
                key: key,
                token: token,
                language: language,
                summary_length: summary_length,
                query_source: query_source,
                net_method: net_method,
                search_method: search_method,
                deep_search_enabled: deep_search_enabled,
                selected_doc_ids: selected_doc_ids,
                enable_thinking: enable_thinking,
                max_results: max_results
            };

            console.log(data);
            // 发送数据到后端
            fetch('/api/init_fileschat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && showAlert) {
                    alert('设置已保存');
                }
            })
            .catch((error) => {
                console.error('Error:', error);
                if (showAlert) {
                    alert('保存设置失败');
                }
            });
        }

        async function fetchUserFiles(username) {
            const response = await fetch(`/api/ragfiles?username=${username}`, {
                method: 'GET',
                cache: 'no-cache'  // 严格不使用缓存
            });
            if (response.ok) {
                const filesWithDocIds = await response.json();
                displayFiles(filesWithDocIds);
            } else {
                console.error('Failed to fetch files:', response.statusText);
            }
        }

        function displayFiles(filesWithDocIds) {
                const container = document.getElementById('file-selection-container');
                container.innerHTML = ''; // 清空容器

                for (const [fileName, docId] of Object.entries(filesWithDocIds)) {
                    const fileCard = document.createElement('div');
                    fileCard.className = 'file-item';
                    fileCard.innerHTML = `
                        <div class="file-info" onclick="toggleCheckbox(this.parentElement)">
                            <input type="checkbox" id="${docId}" value="${docId}">
                            <label for="${docId}" class="file-name">${fileName}</label>
                        </div>
                        <button class="delete-button" onclick="deleteFile('${user}', '${key}', '${token}', '${fileName}')">删除</button>
                    `;
                    container.appendChild(fileCard);
                }
            }

        let allSelected = false; // 用于跟踪全选状态

        function toggleCheckbox(fileItem) {
            const checkbox = fileItem.querySelector('input[type="checkbox"]');
            checkbox.checked = !checkbox.checked; // 切换复选框状态
            updateSelectedDocIds(checkbox); // 更新选中的 doc_id

            // 更新选中样式
            if (checkbox.checked) {
                fileItem.classList.add('selected'); // 添加选中样式
            } else {
                fileItem.classList.remove('selected'); // 移除选中样式
            }
        }

        function toggleSelectAll() {
            const checkboxes = document.querySelectorAll('#file-selection-container input[type="checkbox"]');
            allSelected = !allSelected; // 切换全选状态
            checkboxes.forEach(checkbox => {
                checkbox.checked = allSelected; // 设置每个复选框的状态
                
                // 更新选中样式
                const fileItem = checkbox.closest('.file-item'); // 获取文件项
                if (fileItem) {
                    if (allSelected) {
                        fileItem.classList.add('selected'); // 添加选中样式
                    } else {
                        fileItem.classList.remove('selected'); // 移除选中样式
                    }
                }
                
                updateSelectedDocIds(checkbox); // 更新选中的 doc_id
            });
        }

        function deleteFile(user, key, token, fileName) {
            if (confirm(`确定要删除文件 "${fileName}" 吗？`)) {
                fetch(`/api/del_file`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username: user, key: key, token: token, fileName: fileName })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('文件已成功删除！');
                        fetchUserFiles(user); // 重新获取文件列表
                    } else {
                        alert('删除文件时出错：' + data.message);
                    }
                })
                .catch((error) => {
                    console.error('Error:', error);
                    alert('发生错误，请重试。');
                });
            }
        }

        // 假设这是一个简单的示例函数，用于更新选中的文件 ID
        function updateSelectedDocIds(checkbox) {
            const selectedIds = [];
            const checkboxes = document.querySelectorAll('#file-selection-container input[type="checkbox"]');
            checkboxes.forEach(cb => {
                if (cb.checked) {
                    selectedIds.push(cb.value); // 收集选中的文件 ID
                }
            });
            console.log('Selected IDs:', selectedIds); // 你可以根据需要处理这些 ID
            
            // 将选中的 ID 直接存储为逗号分隔的字符串
            sessionStorage.setItem('selected_doc_ids', selectedIds.join(','));
        }

        fetchUserFiles(user);

        function toggleFileSelection() {
            const fileSelectionContainer = document.getElementById('file-selection-container');
            const toggleButton = document.getElementById('toggle-button'); // 获取按钮元素
            const toggleText = toggleButton.querySelector('.toggle-text'); // 获取按钮文本元素
            
            if (fileSelectionContainer.style.display === 'none') {
                // 显示选项卡
                fileSelectionContainer.style.display = 'grid'; // 显示选项卡
                toggleText.textContent = '隐藏选项卡'; // 更新按钮文本
                toggleButton.classList.remove('toggle-button-collapsed'); // 移除隐藏状态类
                toggleButton.classList.add('toggle-button-expanded'); // 添加展开状态类
                
                // 添加动画效果
                fileSelectionContainer.style.opacity = '0';
                fileSelectionContainer.style.transform = 'translateY(-10px)';
                setTimeout(() => {
                    fileSelectionContainer.style.opacity = '1';
                    fileSelectionContainer.style.transform = 'translateY(0)';
                }, 10);
            } else {
                // 隐藏选项卡
                fileSelectionContainer.style.opacity = '1';
                fileSelectionContainer.style.transform = 'translateY(0)';
                
                // 添加淡出动画
                fileSelectionContainer.style.opacity = '0';
                fileSelectionContainer.style.transform = 'translateY(-10px)';
                
                setTimeout(() => {
                    fileSelectionContainer.style.display = 'none'; // 隐藏选项卡
                    toggleText.textContent = '展开选项卡'; // 更新按钮文本
                    toggleButton.classList.remove('toggle-button-expanded'); // 移除展开状态类
                    toggleButton.classList.add('toggle-button-collapsed'); // 添加隐藏状态类
                }, 300); // 等待动画完成
            }
        }

        // 确保在页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化：通过内联样式和JS双重保证隐藏这三个容器
            const paperCountContainer = document.getElementById('paper_count_container');
            const paperCountContainerProquest = document.getElementById('paper_count_container_proquest');
            const webdeepsearch = document.getElementById('web_deep_search');
            
            // 确保默认状态是隐藏的
            if (paperCountContainer) {
                paperCountContainer.style.display = 'none';
                paperCountContainer.setAttribute('hidden', '');
            }
            if (paperCountContainerProquest) {
                paperCountContainerProquest.style.display = 'none';
                paperCountContainerProquest.setAttribute('hidden', '');
            }
            if (webdeepsearch) {
                webdeepsearch.style.display = 'none';
                webdeepsearch.setAttribute('hidden', '');
            }
            
            // 添加事件监听器
            const searchMethod = document.getElementById('search_method');
            if (searchMethod) {
                // 初始状态下立即检查一次
                togglePaperCount();
                toggledeepsearch();
                
                // 添加change事件监听
                searchMethod.addEventListener('change', function() {
                    togglePaperCount();
                    toggledeepsearch();
                });
            }
            
            // 为输入框添加快捷键功能
            const userInput = document.getElementById('userInput');
            if (userInput) {
                // 监听按键
                userInput.addEventListener('keydown', function(e) {
                    // Ctrl+Enter 或 Command+Enter (Mac) 发送消息
                    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                        e.preventDefault(); // 阻止默认行为
                        sendMessage();
                    }
                    
                    // 动态调整输入框高度
                    setTimeout(() => {
                        userInput.style.height = 'auto';
                        userInput.style.height = Math.min(userInput.scrollHeight, 200) + 'px';
                    }, 0);
                });
                
                // 监听输入事件以自动调整高度
                userInput.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = Math.min(this.scrollHeight, 200) + 'px';
                });
                
                // 输入框获得焦点时添加占位符提示
                userInput.addEventListener('focus', function() {
                    // 为输入区域添加焦点样式
                    const chatInput = document.querySelector('.chat-input');
                    if (chatInput) {
                        chatInput.classList.add('chat-input-focused');
                    }
                    
                    const placeholderTip = document.createElement('div');
                    placeholderTip.className = 'input-tip';
                    placeholderTip.textContent = '按 Ctrl+Enter 发送';
                    
                    // 添加到父容器
                    const wrapper = userInput.closest('.chat-input-wrapper');
                    if (wrapper && !wrapper.querySelector('.input-tip')) {
                        wrapper.appendChild(placeholderTip);
                        
                        // 淡入效果
                        setTimeout(() => {
                            placeholderTip.style.opacity = '1';
                        }, 100);
                        
                        // 当输入框失去焦点时移除提示
                        userInput.addEventListener('blur', function onBlur() {
                            // 淡出效果
                            placeholderTip.style.opacity = '0';
                            
                            // 移除焦点样式
                            if (chatInput) {
                                chatInput.classList.remove('chat-input-focused');
                            }
                            
                            // 移除元素
                            setTimeout(() => {
                                if (placeholderTip.parentNode) {
                                    placeholderTip.parentNode.removeChild(placeholderTip);
                                }
                            }, 300);
                            
                            userInput.removeEventListener('blur', onBlur);
                        });
                    }
                });
            }
            
            // 应用页面加载效果
            applyFadeInEffect();
            
            // 延迟应用增强效果以确保页面完全加载
            setTimeout(function() {
                pageLoadEnhancements();
                setupMutationObserver();
            }, 500);
        });


        function toggledeepsearch() {
            const searchMethod = document.getElementById('search_method');
            const webdeepsearch = document.getElementById('web_deep_search');
            
            if (searchMethod && webdeepsearch) {
                console.log("当前生成方法:", searchMethod.value);
                // 只在"本地"、"Arxiv论文搜索"和"ProQuest论文搜索"选中时显示深度搜索选项
                if (searchMethod.value === '本地' || searchMethod.value === '论文搜索' || searchMethod.value === '论文搜索1') {
                    webdeepsearch.style.cssText = "flex: 0 0 190px; display: flex !important; align-items: center; border: 1px solid #E6B84D; border-radius: 5px; padding: 5px; min-width: 190px; max-width: 190px;";
                    webdeepsearch.removeAttribute('hidden');
                    console.log("显示深度搜索框");
                } else {
                    webdeepsearch.style.cssText = "flex: 0 0 190px; display: none !important; align-items: center; border: 1px solid #E6B84D; border-radius: 5px; padding: 5px; min-width: 190px; max-width: 190px;";
                    webdeepsearch.setAttribute('hidden', '');
                    console.log("隐藏深度搜索框");
                }
            }
        }

        function togglePaperCount() {
            const searchMethod = document.getElementById('search_method');
            const paperCountContainer = document.getElementById('paper_count_container');
            const paperCountContainerProquest = document.getElementById('paper_count_container_proquest');
            
            if (searchMethod) {
                console.log("切换生成方法:", searchMethod.value);
                // 默认隐藏所有容器
                if (paperCountContainer) {
                    paperCountContainer.style.cssText = "flex: 0 0 320px; display: none !important; align-items: center; border: 1px solid #E6B84D; border-radius: 5px; padding: 8px 12px; min-width: 320px; max-width: 320px; justify-content: space-between;";
                    paperCountContainer.setAttribute('hidden', '');
                }
                if (paperCountContainerProquest) {
                    paperCountContainerProquest.style.cssText = "flex: 0 0 320px; display: none !important; align-items: center; border: 1px solid #E6B84D; border-radius: 5px; padding: 8px 12px; min-width: 320px; max-width: 320px; justify-content: space-between;";
                    paperCountContainerProquest.setAttribute('hidden', '');
                }
                
                // 根据选择的搜索方法显示对应的容器
                if (searchMethod.value === '论文搜索') {
                    // 仅当选择Arxiv论文搜索时显示Arxiv数量框
                    if (paperCountContainer) {
                        paperCountContainer.style.cssText = "flex: 0 0 320px; display: flex !important; align-items: center; border: 1px solid #E6B84D; border-radius: 5px; padding: 8px 12px; min-width: 320px; max-width: 320px; justify-content: space-between;";
                        paperCountContainer.removeAttribute('hidden');
                        console.log("显示Arxiv论文检索数量框");
                    }
                } else if (searchMethod.value === '论文搜索1') {
                    // 仅当选择ProQuest论文搜索时显示ProQuest数量框
                    if (paperCountContainerProquest) {
                        paperCountContainerProquest.style.cssText = "flex: 0 0 320px; display: flex !important; align-items: center; border: 1px solid #E6B84D; border-radius: 5px; padding: 8px 12px; min-width: 320px; max-width: 320px; justify-content: space-between;";
                        paperCountContainerProquest.removeAttribute('hidden');
                        console.log("显示ProQuest论文检索数量框");
                    }
                }
            }
        }
        
        // 增强的复制代码按钮功能
        function enhanceCopyButtons() {
            document.querySelectorAll('pre').forEach(function(preElement) {
                // 移除旧的复制按钮（如果有）
                const oldButton = preElement.querySelector('.copy-code-button');
                if (oldButton) {
                    oldButton.remove();
                }
                
                // 创建代码块头部
                const header = document.createElement('div');
                header.className = 'code-header';
                
                // 检测语言类型
                let language = 'code';
                const codeElement = preElement.querySelector('code');
                if (codeElement && codeElement.className) {
                    const classList = codeElement.className.split(' ');
                    for (const className of classList) {
                        if (className.startsWith('language-')) {
                            language = className.substring(9);
                            break;
                        }
                    }
                }
                
                // 添加语言标识
                const langDisplay = document.createElement('span');
                langDisplay.textContent = language !== 'code' ? language : 'code';
                header.appendChild(langDisplay);
                
                // 添加复制按钮
                const button = document.createElement('button');
                button.textContent = '复制';
                button.className = 'copy-button';
                
                button.addEventListener('click', function() {
                    const code = codeElement ? codeElement.textContent : preElement.textContent;
                    
                    navigator.clipboard.writeText(code).then(function() {
                        // 成功复制的动效反馈
                        button.textContent = '已复制 ✓';
                        button.style.backgroundColor = '#4CAF50';
                        button.style.color = 'white';
                        button.style.borderColor = '#4CAF50';
                        
                        // 3秒后恢复按钮状态
                        setTimeout(function() {
                            button.textContent = '复制';
                            button.style.backgroundColor = '';
                            button.style.color = '';
                            button.style.borderColor = '';
                        }, 3000);
                    }).catch(function() {
                        // 复制失败的反馈
                        button.textContent = '复制失败 ×';
                        button.style.backgroundColor = '#f44336';
                        button.style.color = 'white';
                        
                        // 3秒后恢复按钮状态
                        setTimeout(function() {
                            button.textContent = '复制';
                            button.style.backgroundColor = '';
                            button.style.color = '';
                        }, 3000);
                    });
                });
                
                header.appendChild(button);
                
                // 在pre元素前插入头部
                preElement.parentNode.insertBefore(header, preElement);
                
                // 给按钮和pre元素添加样式类
                preElement.classList.add('with-header');
            });
        }
        
        // 为消息添加微动效
        function enhanceMessageAnimations() {
            document.querySelectorAll('.system-message, .you-message, .ai-message').forEach(message => {
                // 只有当元素没有被增强过时才添加
                if (!message.dataset.enhanced) {
                    message.dataset.enhanced = 'true';
                    message.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease';
                    message.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateY(-2px)';
                        this.style.boxShadow = '0 6px 12px rgba(0, 0, 0, 0.1)';
                    });
                    message.addEventListener('mouseleave', function() {
                        this.style.transform = 'translateY(0)';
                        this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.05)';
                    });
                }
            });
        }
        
        // 页面加载完成效果和增强
        function pageLoadEnhancements() {
            // 应用代码块复制功能
            enhanceCopyButtons();
            
            // 应用消息动画效果
            enhanceMessageAnimations();
            
            // 应用按钮样式
            document.querySelectorAll('#sendButton, #newChatButton, #downloadButton, .toggle-button, .settings-submit').forEach(button => {
                if (!button.classList.contains('enhanced')) {
                    button.classList.add('enhanced');
                    button.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateY(-2px)';
                    });
                    button.addEventListener('mouseleave', function() {
                        this.style.transform = 'translateY(0)';
                    });
                }
            });
        }
        
        // 修改文件项的鼠标悬停效果
        function enhanceFileItems() {
            document.querySelectorAll('.file-item').forEach(item => {
                if (!item.dataset.enhanced) {
                    item.dataset.enhanced = 'true';
                    item.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateY(-2px)';
                        this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.08)';
                    });
                    item.addEventListener('mouseleave', function() {
                        this.style.transform = 'translateY(0)';
                        this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.03)';
                    });
                }
            });
        }
        
        // 初始页面加载效果
        function applyFadeInEffect() {
            const elements = [
                document.querySelector('.chat-header'),
                document.querySelector('.chat-history'),
                document.querySelector('.chat-input'),
                document.querySelector('.file-selection')
            ];
            
            elements.forEach((el, index) => {
                if (el) {
                    el.style.opacity = '0';
                    el.style.transition = 'opacity 0.5s ease';
                    
                    setTimeout(() => {
                        el.style.opacity = '1';
                    }, index * 150);
                }
            });
        }
        
        // 监控DOM变化，自动应用增强
        function setupMutationObserver() {
            const observer = new MutationObserver(function(mutations) {
                let needsEnhancement = false;
                
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // 检查是否有新的消息或代码块添加
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1) { // 元素节点
                                if (node.classList && 
                                   (node.classList.contains('system-message') || 
                                    node.classList.contains('you-message') || 
                                    node.classList.contains('ai-message'))) {
                                    needsEnhancement = true;
                                }
                                
                                // 检查是否有新的代码块
                                if (node.querySelectorAll) {
                                    const newPreElements = node.querySelectorAll('pre');
                                    if (newPreElements.length > 0) {
                                        needsEnhancement = true;
                                    }
                                    
                                    // 检查是否有新的文件项
                                    const newFileItems = node.querySelectorAll('.file-item');
                                    if (newFileItems.length > 0) {
                                        enhanceFileItems();
                                    }
                                }
                            }
                        });
                    }
                });
                
                if (needsEnhancement) {
                    setTimeout(function() {
                        enhanceCopyButtons();
                        enhanceMessageAnimations();
                    }, 100);
                }
            });
            
            // 开始观察文档变化
            observer.observe(document.body, { 
                childList: true, 
                subtree: true 
            });
        }
    </script>
</body>
</html>